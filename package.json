{"name": "siya", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host --mode development", "build:test": "vite build --mode test", "build:stage": "vite build --mode staging", "build": "vite build --mode production", "build:pro-va": "vite build --mode production-va", "build:pro-fra": "vite build --mode production-fra", "build:pro-uae": "vite build --mode production-uae", "build:puhua": "vite build --mode puhua", "preview": "vite preview", "i18n": "node translate.mjs"}, "dependencies": {"@vant/touch-emulator": "^1.4.0", "axios": "^1.6.2", "clipboard": "^2.0.11", "dayjs": "^1.11.13", "echarts": "^5.6.0", "esdk-obs-browserjs": "^3.24.3", "libpag": "^4.2.84", "lodash": "^4.17.21", "mitt": "^3.0.1", "pinia": "^2.3.0", "qiniu-js": "^4.0.0-beta.6", "svgaplayerweb": "^2.3.2", "swiper": "^11.1.14", "thinkingdata-browser": "^2.1.1", "vant": "^4.8.11", "vue": "^3.3.11", "vue-i18n": "^9.13.1", "vue-router": "^4.2.5"}, "devDependencies": {"@vant/auto-import-resolver": "^1.2.0", "@vitejs/plugin-legacy": "^5.4.2", "@vitejs/plugin-vue": "^4.5.2", "dotenv": "^16.4.5", "node-file-dialog": "^1.0.3", "node-xlsx": "^0.24.0", "sass": "~1.69.5", "terser": "^5.31.6", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.26.0", "vconsole": "^3.15.1", "vite": "^5.0.8", "vite-plugin-compression": "^0.5.1", "vite-plugin-svg-icons": "^2.0.1"}, "volta": {"node": "20.19.3", "npm": "11.4.2"}}