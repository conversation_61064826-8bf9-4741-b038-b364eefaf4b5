<template>
  <!-- 顶部安全区 -->
  <!--  <div class="van-safe-area-top"></div>-->

  <router-view v-slot="{ Component }">
    <keep-alive>
      <component
        :is="Component"
        :key="$route.fullPath"
        v-if="$route.meta.keepAlive"
      />
    </keep-alive>
    <component
      :is="Component"
      :key="$route.fullPath"
      v-if="!$route.meta.keepAlive"
    />
  </router-view>
  <reward-preview ref="rewardPreviewRef"></reward-preview>

  <!-- 底部安全区 -->
  <!--  <div class="van-safe-area-bottom"></div>-->
</template>

<script setup>
import { getCurrentInstance, onMounted, ref } from "vue";
import { resetDocumentRtl, setPageTitle18n } from "@/i18n/index.js";
import { useRoute } from "vue-router";
import emitter from "@/utils/emitter.js";

const { proxy } = getCurrentInstance();
const route = useRoute();
const rewardPreviewRef = ref();

onMounted(() => {
  // proxy.$app.config.globalProperties.$languageFile = checkLang()
  resetDocumentRtl();
  setPageTitle18n(route.name);
  emitter.on("reward-preview", rewardPreviewRef.value?.preview);
});
</script>

<style lang="scss">
@import "@/assets/scss/common.scss";

#app {
  min-height: 100vh;
  margin: 0 auto;
  background-color: $bgColorB7;
}
</style>
