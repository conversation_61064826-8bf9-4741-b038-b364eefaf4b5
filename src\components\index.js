import Gap from "./gap/index.vue";
import HeaderBar from "./header-bar/index.vue";
import Avatar from "./avatar/index.vue";
import BaseModal from "./base-modal/index.vue";
import Empty from "./empty/index.vue";
import CoinNumber from "./coin-number/index.vue";
import Flag from "./flag/index.vue";
import Photo from "./photo/index.vue";
import ProgressBar from "./progressBar/inxe.vue";
import RewardPreview from "./reward-preview/index.vue";

export default {
  install(Vue) {
    Vue.component("Gap", Gap);
    Vue.component("HeaderBar", HeaderBar);
    Vue.component("Avatar", Avatar);
    Vue.component("BaseModal", BaseModal);
    Vue.component("Empty", Empty);
    Vue.component("CoinNumber", CoinNumber);
    Vue.component("Flag", Flag);
    Vue.component("Photo", Photo);
    Vue.component("ProgressBar", ProgressBar);
    Vue.component("RewardPreview", RewardPreview);
  },
};
