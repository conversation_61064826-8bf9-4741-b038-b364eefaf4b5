<template>
  <van-overlay :show="show" @touchmove.prevent @click="close()" z-index="99999">
    <div class="base-modal-wrapper">
      <div
        v-if="isSvga(source)"
        dir="ltr"
        :id="'guide' + timeId"
        class="svga"
      ></div>
      <video
        v-if="isMP4(source)"
        ref="videoRef"
        autoplay
        loop
        muted
        playsinline
        class="video"
      >
        <source :src="source" type="video/webm" />
      </video>
    </div>
  </van-overlay>
</template>
<script setup>
import { nextTick, onMounted, ref } from "vue";
import SVGA from "svgaplayerweb";

const videoRef = ref();
const timeId = ref(Math.floor(new Date().getTime() * Math.random())); // 使该图表保持唯一id
const show = ref(false);
const source = ref(null);

let svgaPlayer = null;
let svgaParser = null;

const initSvga = () => {
  // 获取id的dom元素
  svgaPlayer = new SVGA.Player(`#guide${timeId.value}`);
  svgaParser = new SVGA.Parser();
};

const isSvga = (src) => {
  return src.split("?")[0].endsWith(".svga");
};

const isMP4 = (src) => {
  return src.split("?")[0].endsWith(".mp4");
};

const preview = async (src) => {
  source.value = src;
  show.value = true;
  if (isSvga(source.value)) {
    nextTick(() => {
      initSvga();
      svgaParser.load(source.value, (videoItem) => {
        svgaPlayer.setVideoItem(videoItem);
        svgaPlayer.startAnimation();
      });
    });
  } else if (isMP4(source.value)) {
    videoRef.value?.play();
  }
};

const close = () => {
  show.value = false;
};

defineExpose({
  preview,
});
</script>
<style lang="scss" scoped>
.prize-dialog {
  --van-overlay-background: rgba(0, 0, 0, 0.88);
}
@keyframes show {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes cover {
  0% {
    transform: scale(0.2);
    opacity: 0;
  }
  60% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.base-modal-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.svga,
.video {
  width: 100%;
  height: 100%;
}

.video {
  mix-blend-mode: screen; /* 使黑色背景透明 */
}
</style>
