{"withdrawal_rule_title": "প্রত্যা<PERSON><PERSON><PERSON> নির্দে<PERSON><PERSON><PERSON><PERSON>ী,", "withdrawal_rule_detail": ["একটি সফল প্রত্যাহারের অনুরোধের পরে, তহব<PERSON><PERSON>গুলি 1-7 কার্যদিবসের মধ্যে পৌঁছানোর প্রত্যাশিত৷ যদি আনুমানিক সময়ের মধ্যে তহবিল না আসে, অনুগ্রহ করে নিশ্চিত করুন যে আপনার গ্রহণ করা অ্যাকাউন্টটি সঠিক এবং ধৈর্য ধরে অপেক্ষা করুন বা গ্রাহক পরিষেবার সাথে যোগাযোগ করুন৷", "প্রত্যাহারের পরিমাণের উপর ভিত্তি করে বিভিন্ন প্রত্যাহার ফি প্রয়োজন, নির্দিষ্ট পরিমাণ প্রকৃত অর্থ প্রাপ্তির সাপেক্ষে।,", "আপনি যদি আপনার অ্যাকাউন্ট বাতিল করেন বা প্রত্যাহারের সময়কালে লঙ্ঘনের কারণে নিষিদ্ধ হন, আমরা আপনার প্রত্যাহারের আদেশটি হিমায়িত করব।"], "withdrawal_select_methods_tips": " প্রত্যাহার করার জন্য আপনার অর্থপ্রদানের পদ্ধতি বাধ্যতামূলক করতে হবে। অনুগ্রহ করে আপনার অর্থপ্রদানের পদ্ধতি নির্বাচন করুন এবং নিশ্চিত করুন যে আপনার অর্থপ্রদানের তথ্য সত্য এবং বৈধ।,", "service_fee": "হ্যান্ডলিং চার্জ: সার্ভিস_ফি", "withdrawal_methods": "পেমেন্ট পদ্ধতি,", "withdrawal_information": "প্রত্যাহার তথ্য,", "withdrawal_info_check": "অনুগ্রহ করে আপনার প্রত্যাহারের তথ্য নিশ্চিত করুন।,", "estimated_withdrawal_amount": "প্রত্যাহারের আনুমানিক পরিমাণ,", "estimated_amount_received": "প্রাপ্তির আনুমানিক পরিমাণ,", "estimated_service_fee": "আনুমানিক হ্যান্ডলিং চার্জ,", "current_selected": "বর্তমান নির্বাচন,", "completed": "ইতিমধ্যে ভর্তি,", "need_complete": "সম্পূর্ণ হতে হবে,", "withdrawal_amount": "প্রত্যাহার পরিমাণ,", "total_withdrawal_amount": "ক্রমবর্ধমান প্রত্যাহারের পরিমাণ,", "withdrawal_pending": "প্রত্যাহার চলছে,", "withdrawal_success": "প্রত্যাহার সফল,", "withdrawal_fail": "প্রত্যাহার ব্যর্থ হয়েছে,", "fail_reason": "প্রত্যাহার ব্যর্থতার কারণ,", "empty": "কোনো প্রত্যাহারের রেকর্ড নেই,", "BANK_TRANSFER_receive_bank": "প্রাপক ব্যাঙ্ক,", "BANK_TRANSFER_receive_bank_placeholder": "প্রাপক ব্যাঙ্ক নির্বাচন করুন,", "BANK_TRANSFER_fill_info_tips": "অনুগ্রহ করে আপনার পেমেন্টের তথ্য পূরণ করুন,", "payeeInfo_documentId_title": "CPF/CNPJ,", "payeeInfo_documentId_placeholder": "যেমন.***********,", "payeeInfo_documentId_hint": "দয়া করে CPF/CNPJ (ব্যক্তি/কর্পোরেট ট্যাক্স নম্বর) লিখুন,", "payeeInfo_address_title": "ঠিকানা,", "payeeInfo_email_title": "ইমেইল,", "payeeInfo_email_placeholder": "#VALUE!", "payeeInfo_email_hint": "দয়া করে সঠিক ইমেল বিন্যাস লিখুন,", "payeeInfo_phone_title": "ফোন নম্বর,", "WALLET_accountNo_title": "{0} অ্যাকাউন্ট,", "WALLET_accountNo_placeholder_SA": "যেমন.************,", "WALLET_accountNo_placeholder_EG": "যেমন ***********,", "WALLET_accountNo_placeholder_AE": "যেমন +971-*********,", "WALLET_accountNo_placeholder_TR": "যেমন **********,", "WALLET_accountNo_placeholder_PH": "যেমন ***********,", "WALLET_accountNo_placeholder_BR_MERCADOPAGO": "#VALUE!", "WALLET_accountNo_placeholder_BR_PIX": "যেমন ***********,", "WALLET_accountNo_placeholder_BR_PagBank": "#VALUE!", "WALLET_accountNo_placeholder_ID": "যেমন ***********", "WALLET_accountNo_placeholder_MY": "যেমন **********", "WALLET_accountNo_placeholder_TH": "যেমন **********", "WALLET_accountNo_placeholder_VN": "যেমন ***********", "WALLET_accountNo_hint_SA": "আপনার ওয়ালেটে আবদ্ধ ফোন নম্বরটি লিখুন, 9665 দিয়ে শুরু হওয়া 12 সংখ্যা,", "WALLET_accountNo_hint_EG": "আপনার ওয়ালেটে আবদ্ধ ফোন নম্বরটি লিখুন, 01 দিয়ে শুরু হওয়া 11 সংখ্যা,", "WALLET_accountNo_hint_EG_2": "অনুগ্রহ করে আপনার ওয়ালেটে আবদ্ধ ফোন নম্বর লিখুন, 01 দিয়ে শুরু হওয়া 12 সংখ্যা বা দেশের কোড প্লাস 10 সংখ্যা,", "WALLET_accountNo_hint_EG_MEEZA_NETWORK": "প্রদানকারীর ফোন নম্বর-প্রয়োজনীয়-মানিব্যাগে আবদ্ধ ফোন নম্বর，01 বা 201 দিয়ে শুরু হওয়া 11 সংখ্যা,", "WALLET_accountNo_hint_AE": "অনুগ্রহ করে আপনার ওয়ালেটে আবদ্ধ ফোন নম্বরটি লিখুন৷ বিন্যাসটি অবশ্যই হতে হবে৷", "WALLET_accountNo_hint_TR": "অনুগ্রহ করে আপনার ওয়ালেটে আবদ্ধ ফোন নম্বর লিখুন, একটি 10 সংখ্যা বা PL প্লাস 10 সংখ্যা,", "WALLET_accountNo_hint_PH": "আপনার ওয়ালেটে আবদ্ধ ফোন নম্বরটি লিখুন, 09 দিয়ে শুরু হওয়া 11 সংখ্যা,", "WALLET_accountNo_hint_PH_LAZADAPH": "অনুগ্রহ করে আপনার ওয়ালেটে আবদ্ধ ফোন নম্বর লিখুন, 09 বা ইমেল দিয়ে শুরু হওয়া 11 সংখ্যা,", "WALLET_accountNo_hint_BR_MERCADOPAGO": "অনুগ্রহ করে ওয়ালেটে আবদ্ধ ব্যবহারকারী অ্যাকাউন্ট (ইমেল বা আইডি) লিখুন,", "WALLET_accountNo_hint_BR_PIX": "অনুগ্রহ করে ওয়ালেটের সাথে আবদ্ধ সংশ্লিষ্ট অ্যাকাউন্টটি প্রবেশ করান,", "WALLET_accountNo_hint_BR_PagBank": "আপনার ওয়ালেটে আবদ্ধ ইমেল ঠিকানা লিখুন, 60টি অক্ষর পর্যন্ত,", "WALLET_accountNo_hint_ID": "অনুগ্রহ করে আপনার ওয়ালেট-লিঙ্ক করা অ্যাকাউন্ট নম্বর লিখুন: 9 থেকে 14 সংখ্যা 08 দিয়ে শুরু", "WALLET_accountNo_hint_MY": "অনুগ্রহ করে আপনার ওয়ালেট-সংযুক্ত অ্যাকাউন্ট নম্বর লিখুন: 10 বা 11 সংখ্যা 0 দিয়ে শুরু", "WALLET_accountNo_hint_TH": "অনুগ্রহ করে আপনার ওয়ালেট-লিঙ্ক করা অ্যাকাউন্ট নম্বর লিখুন: 0 দিয়ে শুরু হওয়া 10 সংখ্যা ", "WALLET_accountNo_hint_VN": "অনুগ্রহ করে আপনার ওয়ালেট-লিঙ্ক করা অ্যাকাউন্ট নম্বর লিখুন: 84 দিয়ে শুরু হওয়া 11 সংখ্যা", "WALLET_fullName_title": "প্রাপকের নাম,", "WALLET_fullName_placeholder": "যেমন আন্তোনিও, মালডোনাডো, ইভাঞ্জেলিস্তা", "WALLET_fullName_placeholder_AE": "যেমন লি মিং,", "WALLET_fullName_placeholder_PH": "যেমন আন্তোনিও,মালডোনাডো,ইভাঞ্জেলিস্তা,", "WALLET_fullName_placeholder_BR": "যেমন আন্তোনিও মালডোনাডো ইভাঞ্জেলিস্তা,", "WALLET_fullName_hint": "অনুগ্রহ করে আপনার সম্পূর্ণ ইংরেজি নাম লিখুন,", "WALLET_lastName_title": "পাইয়ের শেষ নাম,", "WALLET_lastName_placeholder_EG": "যেমন ইভাঞ্জেলিস্তা,", "WALLET_lastName_hint": "অনুগ্রহ করে আপনার শেষ নাম লিখুন,", "WALLET_accountType_title": "{0} বাইন্ডিং অ্যাকাউন্টের ধরন,", "WALLET_accountType_placeholder_BR_PIX": "দয়া করে বাইন্ডিং অ্যাকাউন্টের ধরন নির্বাচন করুন,", "WALLET_accountType_option_E_PIX": "ইমেইল,", "WALLET_accountType_option_P_PIX": "ফোন,", "WALLET_accountType_option_C_PIX": "CPF/CNPJ (ব্যক্তিগত/কর্পোরেট ট্যাক্স নম্বর),", "WALLET_accountType_option_B_PIX": "EVP,", "WALLET_accountNo_placeholder_BR_PIX_B": "যেমন। 123e4567-e89b-12d3-a456-************,", "SWIFT_accountNo_title": "SWIFT অ্যাকাউন্ট,", "SWIFT_accountNo_placeholder_SA": "যেমন SA44200000**********1234,", "SWIFT_accountNo_placeholder_AE": "যেমন AE46009000000**********,", "SWIFT_accountNo_placeholder_KW": "যেমন ******************************,", "SWIFT_accountNo_placeholder_QA": "যেমন *****************************,", "SWIFT_accountNo_placeholder_TR": "যেমন TR32001000999990**********,", "SWIFT_accountNo_placeholder_BH": "যেমন **********************,", "SWIFT_accountNo_placeholder_YE": "যেমন.**********,", "SWIFT_accountNo_placeholder_IQ": "যেমন ***********************,", "SWIFT_accountNo_placeholder_IL": "যেমন ***********************,", "SWIFT_accountNo_placeholder_PS": "যেমন.**********,", "SWIFT_accountNo_placeholder_AZ": "যেমন AZ77VTBA0000000000**********,", "SWIFT_accountNo_placeholder_LB": "যেমন ****************************,", "SWIFT_accountNo_placeholder_MA": "যেমন.**********,", "SWIFT_accountNo_placeholder_PH": "যেমন.**********,", "SWIFT_accountNo_placeholder_BR": "যেমন *****************************,", "SWIFT_accountNo_placeholder_EG": "যেমন *****************************,", "SWIFT_accountNo_placeholder_JO": "JO71CBJO000000000000**********", "SWIFT_accountNo_hint_SA": "অনুগ্রহ করে আপনার SWIFT অ্যাকাউন্ট লিখুন, SA দিয়ে শুরু হওয়া 24 অক্ষর,", "SWIFT_accountNo_hint_AE": "অনুগ্রহ করে আপনার SWIFT অ্যাকাউন্ট লিখুন, AE দিয়ে শুরু হওয়া 23টি অক্ষর,", "SWIFT_accountNo_hint_KW": "দয়া করে আপনার SWIFT অ্যাকাউন্ট লিখুন, KW দিয়ে শুরু 30 অক্ষর,", "SWIFT_accountNo_hint_QA": "অনুগ্রহ করে আপনার SWIFT অ্যাকাউন্ট লিখুন, QA দিয়ে শুরু 29 অক্ষর,", "SWIFT_accountNo_hint_TR": "অনুগ্রহ করে আপনার SWIFT অ্যাকাউন্ট লিখুন, TR দিয়ে শুরু 26 অক্ষর,", "SWIFT_accountNo_hint_BH": "অনুগ্রহ করে আপনার SWIFT অ্যাকাউন্ট লিখুন, BH দিয়ে শুরু 22 অক্ষর,", "SWIFT_accountNo_hint_YE": "অনুগ্রহ করে আপনার SWIFT অ্যাকাউন্ট, 34 সংখ্যা এবং অক্ষর বা তার কম লিখুন,", "SWIFT_accountNo_hint_IQ": "অনুগ্রহ করে আপনার SWIFT অ্যাকাউন্ট লিখুন, IQ দিয়ে শুরু হওয়া 23 অক্ষর,", "SWIFT_accountNo_hint_IL": "অনুগ্রহ করে আপনার SWIFT অ্যাকাউন্ট লিখুন, IL দিয়ে শুরু হওয়া 23 অক্ষর,", "SWIFT_accountNo_hint_PS": "অনুগ্রহ করে আপনার SWIFT অ্যাকাউন্ট, 34 সংখ্যা এবং অক্ষর বা তার কম লিখুন,", "SWIFT_accountNo_hint_AZ": "অনুগ্রহ করে আপনার SWIFT অ্যাকাউন্ট লিখুন, AZ দিয়ে শুরু হওয়া 28 অক্ষর,", "SWIFT_accountNo_hint_LB": "অনুগ্রহ করে আপনার SWIFT অ্যাকাউন্ট লিখুন, LB দিয়ে শুরু 28 অক্ষর,", "SWIFT_accountNo_hint_MA": "অনুগ্রহ করে আপনার SWIFT অ্যাকাউন্ট, 34 সংখ্যা এবং অক্ষর বা তার কম লিখুন,", "SWIFT_accountNo_hint_PH": "অনুগ্রহ করে আপনার SWIFT অ্যাকাউন্ট, 34 সংখ্যা এবং অক্ষর বা তার কম লিখুন,", "SWIFT_accountNo_hint_BR": "অনুগ্রহ করে আপনার SWIFT অ্যাকাউন্ট লিখুন, BR দিয়ে শুরু 29টি অক্ষর,", "SWIFT_accountNo_hint_EG": "অনুগ্রহ করে আপনার SWIFT অ্যাকাউন্ট লিখুন, EG দিয়ে শুরু 29টি অক্ষর,", "SWIFT_accountNo_hint_JO": "অনুগ্রহ করে আপনার SWIFT অ্যাকাউন্ট লিখুন, J<PERSON> দিয়ে শুরু হওয়া 30টি অক্ষর,", "SWIFT_fullName_title": "প্রাপকের নাম,", "SWIFT_fullName_placeholder": "যেমন লি মিং,", "SWIFT_fullName_hint": "অনুগ্রহ করে আপনার সম্পূর্ণ ইংরেজি নাম লিখুন,", "SWIFT_bankCode_title": "ব্যাঙ্ক নম্বর (SWIFT কোড),", "SWIFT_bankCode_placeholder": "যেমন SCBLHKHH,", "SWIFT_bankCode_hint": "অনুগ্রহ করে ব্যাঙ্ক কোড লিখুন, 8 বা 11 সংখ্যা, সংখ্যা এবং বড় হাতের অক্ষর অনুমোদিত,", "CARD_accountNo_title": "ব্যাংক অ্যাকাউন্ট,", "CARD_accountNo_placeholder_EG": "যেমন ***********,", "CARD_accountNo_placeholder_TR": "যেমন ****************,", "CARD_accountNo_hint_EG": "অনুগ্রহ করে ব্যাঙ্ক অ্যাকাউন্ট লিখুন, দৈর্ঘ্য>=8,", "CARD_accountNo_hint_TR": "অনুগ্রহ করে 16-সংখ্যার ব্যাঙ্ক কার্ড নম্বর লিখুন, শুধুমাত্র ডেবিট কার্ডগুলি সমর্থিত,", "CARD_fullName_title": "প্রাপকের নাম,", "CARD_fullName_placeholder": "যেমন আন্তোনিও মালডোনাডো ইভাঞ্জেলিস্তা,", "CARD_fullName_hint": "অনুগ্রহ করে আপনার সম্পূর্ণ ইংরেজি নাম লিখুন,", "CARRIER_BILLING_accountNo_title": "ফোন নম্বর,", "CARRIER_BILLING_accountNo_placeholder": "যেমন.63**********,", "CARRIER_BILLING_accountNo_hint": "অনুগ্রহ করে ফোন নম্বর লিখুন।,", "CASH_accountNo_title": "{0}অ্যাকাউন্ট,", "CASH_accountNo_placeholder": "যেমন ***********,", "CASH_accountNo_hint": "অনুগ্রহ করে{0}অ্যাকাউন্ট লিখুন, 0 দিয়ে শুরু হওয়া ১১টি সংখ্যা,", "BANK_TRANSFER_accountNo_title_SA": "ব্যাঙ্ক অ্যাকাউন্ট (IBAN),", "BANK_TRANSFER_accountNo_title_AE": "ব্যাঙ্ক অ্যাকাউন্ট (IBAN),", "BANK_TRANSFER_accountNo_title_TR": "ব্যাঙ্ক অ্যাকাউন্ট (IBAN),", "BANK_TRANSFER_accountNo_title": "ব্যাঙ্ক অ্যাকাউন্ট", "BANK_TRANSFER_accountNo_title_EG": "ব্যাংক অ্যাকাউন্ট,", "BANK_TRANSFER_accountNo_title_KW": "ব্যাংক অ্যাকাউন্ট,", "BANK_TRANSFER_accountNo_title_QA": "ব্যাংক অ্যাকাউন্ট,", "BANK_TRANSFER_accountNo_title_MA": "ব্যাঙ্ক অ্যাকাউন্ট (RIB),", "BANK_TRANSFER_accountNo_title_PH": "ব্যাংক অ্যাকাউন্ট,", "BANK_TRANSFER_accountNo_title_BR": "ব্যাংক অ্যাকাউন্ট,", "BANK_TRANSFER_accountNo_placeholder_SA": "যেমন ************************,", "BANK_TRANSFER_accountNo_placeholder_EG": "যেমন ************,", "BANK_TRANSFER_accountNo_placeholder_AE": "যেমন ***********************,", "BANK_TRANSFER_accountNo_placeholder_KW": "যেমন ******************************,", "BANK_TRANSFER_accountNo_placeholder_QA": "যেমন *****************************,", "BANK_TRANSFER_accountNo_placeholder_TR": "যেমন **************************,", "BANK_TRANSFER_accountNo_placeholder_MA": "যেমন.123456789876543212345678,", "BANK_TRANSFER_accountNo_placeholder_PH": "যেমন ************,", "BANK_TRANSFER_accountNo_placeholder_BR": "যেমন ***********,", "BANK_TRANSFER_accountNo_placeholder_JO": "যেমন ******************************,", "BANK_TRANSFER_accountNo_placeholder_MY": "যেমন **********", "BANK_TRANSFER_accountNo_placeholder_TH": "যেমন *********", "BANK_TRANSFER_accountNo_placeholder_ID": "যেমন ***********", "BANK_TRANSFER_accountNo_hint_SA": "অনুগ্রহ করে SA+22 অক্ষর এবং সংখ্যার সমন্বয় লিখুন,", "BANK_TRANSFER_accountNo_hint_EG": "অনুগ্রহ করে IBAN এবং স্থানীয় ব্যাঙ্ক অ্যাকাউন্ট , IBAN লিখুন; 29টি অক্ষর (EG+27 সংখ্যা),", "BANK_TRANSFER_accountNo_hint_AE": "অনুগ্রহ করে AE + 21 সংখ্যা লিখুন, AE অবশ্যই বড় করা হবে,", "BANK_TRANSFER_accountNo_hint_KW": "অনুগ্রহ করে IBAN ফরম্যাটে 50 অক্ষরের কম দৈর্ঘ্য লিখুন,", "BANK_TRANSFER_accountNo_hint_QA": "অনুগ্রহ করে IBAN ফরম্যাটে 50 অক্ষরের কম দৈর্ঘ্য লিখুন,", "BANK_TRANSFER_accountNo_hint_TR": "IBAN ফরম্যাটে TR প্লাস 24 ডিজিট দিয়ে শুরু হওয়া একটি নম্বর লিখুন,", "BANK_TRANSFER_accountNo_hint_MA": "অনুগ্রহ করে 24-সংখ্যার RIB অ্যাকাউন্ট কোড লিখুন।,", "BANK_TRANSFER_accountNo_hint_PH": "অনুগ্রহ করে 6 থেকে 128 সংখ্যা লিখুন,", "BANK_TRANSFER_accountNo_hint_BR": "অনুগ্রহ করে 4 থেকে 15 সংখ্যা লিখুন,", "BANK_TRANSFER_accountNo_hint_JO": "অনুগ্রহ করে JO দিয়ে শুরু হওয়া সংখ্যা এবং অক্ষরের একটি 30-অক্ষরের সমন্বয় লিখুন,", "BANK_TRANSFER_accountNo_hint_MY": "সর্বাধিক 35টি অক্ষর সহ একটি অ্যাকাউন্ট নম্বর লিখুন৷", "BANK_TRANSFER_accountNo_hint_TH": "অনুগ্রহ করে একটি 10-18 সংখ্যার অ্যাকাউন্ট নম্বর লিখুন৷", "BANK_TRANSFER_accountNo_hint_ID": "একটি সংখ্যাসূচক অ্যাকাউন্ট নম্বর লিখুন।", "BANK_TRANSFER_bankBranch_title": "ব্যাংক শাখার নম্বর,", "BANK_TRANSFER_bankBranch_placeholder_BR": "যেমন 1234,", "BANK_TRANSFER_bankBranch_hint_BR": "অনুগ্রহ করে ব্যাঙ্ক শাখার নম্বর ইনপুট করুন, 4~6 সংখ্যা,", "BANK_TRANSFER_address_placeholder_SA": "যেমন aabbcccccccc,", "BANK_TRANSFER_address_placeholder_AE": "যেমন আলিসি স্ট্রিট কাদিকয় ইস্তানবুল,", "BANK_TRANSFER_address_placeholder_KW": "যেমন কুয়েত xx রাস্তা,", "BANK_TRANSFER_address_hint_SA": "অনুগ্রহ করে ঠিকানা লিখুন, 10 থেকে 200 অক্ষরের দৈর্ঘ্যের সীমা, শুধুমাত্র সংখ্যা, পিরিয়ড, বড় হাতের এবং ছোট হাতের অক্ষর এবং স্পেসগুলিকে অনুমতি দেয়।,", "BANK_TRANSFER_address_hint_AE": "অনুগ্রহ করে ঠিকানা লিখুন, 1 থেকে 200 অক্ষরের দৈর্ঘ্য সীমা, ব্যাঙ্ক ঝুঁকি নিয়ন্ত্রণ পরীক্ষা পরিচালনা করবে।,", "BANK_TRANSFER_address_hint_KW": "অনুগ্রহ করে আপনার ঠিকানা লিখুন, দৈর্ঘ্য<=255,", "BANK_TRANSFER_fullName_placeholder_TR": "যেমন আন্তোনিও,", "BANK_TRANSFER_fullName_placeholder_PH": "যেমন আন্তোনিও,", "BANK_TRANSFER_fullName_placeholder_BR": "যেমন আন্তোনিও,", "BANK_TRANSFER_fullName_placeholder_MA": "যেমন জর্জ,", "BANK_TRANSFER_fullName_placeholder_KW": "যেমন জর্জ কিন,", "BANK_TRANSFER_phone_placeholder_PH": "যেমন ***********,", "BANK_TRANSFER_phone_placeholder_BR": "যেমন.*************,", "BANK_TRANSFER_phone_hint_PH": "অনুগ্রহ করে আপনার মোবাইল ফোন নম্বর লিখুন, 11 দিয়ে শুরু হওয়া 0-9 সংখ্যা(0 যোগ করতে হবে),", "BANK_TRANSFER_phone_hint_BR": "অনুগ্রহ করে আপনার মোবাইল ফোন নম্বর লিখুন, 55 দিয়ে শুরু 12-13 সংখ্যা,", "BANK_TRANSFER_bankCode_title_MY": "ব্যাঙ্ক কোড (SWIFT কোড)", "BANK_TRANSFER_bankCode_placeholder_MY": "যেমন CITIHK0001", "BANK_TRANSFER_bankCode_hint_MY": "অক্ষর এবং সংখ্যার একটি 8-11 অক্ষরের সমন্বয় লিখুন।", "BANK_TRANSFER_bankBranch_title_SA": "SWIFT কোড", "BANK_TRANSFER_bankBranch_placeholder_SA": "********", "BANK_TRANSFER_bankBranch_hint_SA": "8 বা 11 বড় হাতের অক্ষর এবং সংখ্যার সংমিশ্রণ লিখুন", "BANK_TRANSFER_city_title_SA": "<PERSON><PERSON><PERSON>", "BANK_TRANSFER_city_placeholder_SA": "উদাহরণস্বরূপ New York", "BANK_TRANSFER_city_hint_SA": "শহরের নাম লিখুন, সর্বোচ্চ 35 অক্ষর", "DLOCAL_BANK_TRANSFER_accountNo_placeholder_MA": "উদাহরণস্বরূপ: 001123211111111111111111", "DLOCAL_BANK_TRANSFER_accountNo_hint_MA": "24 সংখ্যার সংমিশ্রণ লিখুন", "DLOCAL_BANK_TRANSFER_birthday_title_AE": "জন্ম তারিখ", "DLOCAL_BANK_TRANSFER_birthday_placeholder_AE": "উদাহরণস্বরূপ ********", "DLOCAL_BANK_TRANSFER_birthday_hint_AE": "জন্ম তা<PERSON><PERSON><PERSON> লিখুন", "DLOCAL_BANK_TRANSFER_documentType_title_AE": "ডকুমেন্ট প্রকার", "DLOCAL_BANK_TRANSFER_documentType_placeholder_AE": "দলিল প্রকার নির্বাচন করুন", "DLOCAL_BANK_TRANSFER_documentType_option_ID_AE": "আইডি", "DLOCAL_BANK_TRANSFER_documentType_option_PASS_AE": "পাস", "DLOCAL_BANK_TRANSFER_documentId_title_AE": "দলিল আইডি", "DLOCAL_BANK_TRANSFER_documentId_placeholder_ID_AE": "উদাহরণ NNN-NNNN-NNNNNNN-N", "DLOCAL_BANK_TRANSFER_documentId_placeholder_PASS_AE": "উদাহরণস্বরূপ ABCD1234", "DLOCAL_BANK_TRANSFER_documentId_hint_AE": "দলিল আইডি লিখুন", "AGENT_accountNo_Whatsapp_title_ALL": "হোয়াটসঅ্যাপ নম্বর,", "AGENT_accountNo_Whatsapp_placeholder_ALL": "উদাহরণস্বরূপ +90 111 111 11 11,", "AGENT_accountNo_Whatsapp_hint_ALL": "অনুগ্রহ করে আপনার Whatsapp অ্যাকাউন্ট লিখুন,", "AGENT_accountNo_vodafone_title_EG": "ভোডাফোন অ্যাকাউন্ট", "AGENT_fullName_title_ID": "পরিশোধপ্রাপ্ত আইডি", "AGENT_fullName_placeholder_ID": "1111111", "AGENT_fullName_hint_ID": "পরিশোধপ্রাপ্ত আইডি লিখুন", "USDT_blockchain_title_ALL": "ব্লকচেন নাম,", "USDT_address_title_ALL": "মুদ্রা জারি করার ঠিকানা,", "USDT_address_placeholder_ALL": "উদাহরণস্বরূপ 111111111111111111111111,", "USDT_address_hint_ALL": "অনুগ্রহ করে 1, 3, বা bc দিয়ে শুরু হওয়া একটি ঠিকানা লিখুন,", "USDT_address_hint_common": "অনুগ্রহ করে আপনার USDT ঠিকানা লিখুন"}