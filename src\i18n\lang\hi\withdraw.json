{"withdrawal_rule_title": "निकासी निर्देश,", "withdrawal_rule_detail": ["सफल निकासी अनुरोध के बाद, धनराशि 1-7 कार्यदिवसों के भीतर प्राप्त होने की उम्मीद है। यदि धनराशि अनुमानित समय के भीतर प्राप्त नहीं होती है, तो कृपया पुष्टि करें कि आपका प्राप्तकर्ता खाता सही है और धैर्यपूर्वक प्रतीक्षा करें या ग्राहक सेवा से संपर्क करें।,", "निकासी की राशि के आधार पर अलग-अलग निकासी शुल्क लगते हैं, और विशिष्ट राशि प्राप्त वास्तविक राशि पर निर्भर करती है।,", "यदि आप निकासी अवधि के दौरान उल्लंघनों के कारण अपना खाता रद्द कर देते हैं या प्रतिबंधित कर दिए जाते हैं, तो हम आपके निकासी आदेश को रोक देंगे।"], "withdrawal_select_methods_tips": "निकासी के लिए आपकी भुगतान विधि बाध्यकारी होनी चाहिए। कृपया अपनी भुगतान विधि चुनें और सुनिश्चित करें कि आपकी भुगतान जानकारी सत्य और मान्य है।", "service_fee": "हैंडलिंग चार्ज: सेवा शुल्क", "withdrawal_methods": "भुगतान विधि,", "withdrawal_information": "निकासी जानकारी,", "withdrawal_info_check": "कृपया अपनी निकासी जानकारी की पुष्टि करें।", "estimated_withdrawal_amount": "अनुमानित निकासी राशि,", "estimated_amount_received": "प्राप्त होने वाली अनुमानित राशि,", "estimated_service_fee": "अनुमानित हैंडलिंग शुल्क,", "current_selected": "वर्तमान चयन,", "completed": "पहले से भरा हुआ,", "need_complete": "पूरा होना,", "withdrawal_amount": "निकासी राशि,", "total_withdrawal_amount": "संचयी निकासी राशि,", "withdrawal_pending": "वापसी जारी है,", "withdrawal_success": "निकासी सफल,", "withdrawal_fail": "निकासी विफल,", "fail_reason": "निकासी विफलता का कारण,", "empty": "कोई निकासी रिकॉर्ड नहीं,", "BANK_TRANSFER_receive_bank": "भुगतानकर्ता बैंक,", "BANK_TRANSFER_receive_bank_placeholder": "भुगतानकर्ता बैंक का चयन करें,", "BANK_TRANSFER_fill_info_tips": "कृपया अपनी भुगतान जानकारी भरें,", "payeeInfo_documentId_title": "सीपीएफ/सीएनपीजे,", "payeeInfo_documentId_placeholder": "उदाहरण: ***********,", "payeeInfo_documentId_hint": "कृपया CPF/CNPJ (व्यक्तिगत/कॉर्पोरेट कर संख्या) दर्ज करें,", "payeeInfo_address_title": "पता,", "payeeInfo_email_title": "ईमेल,", "payeeInfo_email_placeholder": "#VALUE!", "payeeInfo_email_hint": "कृपया सही ईमेल प्रारूप दर्ज करें,", "payeeInfo_phone_title": "फ़ोन नंबर,", "WALLET_accountNo_title": "{0} खाता,", "WALLET_accountNo_placeholder_SA": "उदाहरण: ************,", "WALLET_accountNo_placeholder_EG": "उदा.***********,", "WALLET_accountNo_placeholder_AE": "उदाहरण:+971-*********,", "WALLET_accountNo_placeholder_TR": "उदा.**********,", "WALLET_accountNo_placeholder_PH": "उदा.***********,", "WALLET_accountNo_placeholder_BR_MERCADOPAGO": "#VALUE!", "WALLET_accountNo_placeholder_BR_PIX": "उदा.***********,", "WALLET_accountNo_placeholder_BR_PagBank": "#VALUE!", "WALLET_accountNo_placeholder_ID": "उदाहरण: ***********", "WALLET_accountNo_placeholder_MY": "उदाहरण: **********", "WALLET_accountNo_placeholder_TH": "उदाहरण: **********", "WALLET_accountNo_placeholder_VN": "उदाहरण: ***********", "WALLET_accountNo_hint_SA": "कृपया अपने वॉलेट से जुड़ा फ़ोन नंबर दर्ज करें, 9665 से शुरू होने वाले 12 अंक,", "WALLET_accountNo_hint_EG": "कृपया अपने वॉलेट से जुड़ा फ़ोन नंबर दर्ज करें, 01 से शुरू होने वाले 11 अंक,", "WALLET_accountNo_hint_EG_2": "कृपया अपने वॉलेट से जुड़ा फ़ोन नंबर दर्ज करें, 01 से शुरू होने वाले 12 अंक या देश कोड + 10 अंक,", "WALLET_accountNo_hint_EG_MEEZA_NETWORK": "भुगतानकर्ता का फ़ोन नंबर-आवश्यक-वॉलेट से जुड़ा फ़ोन नंबर, 01 या 201 से शुरू होने वाले 11 अंक,", "WALLET_accountNo_hint_AE": "कृपया अपने वॉलेट से जुड़ा फ़ोन नंबर दर्ज करें। प्रारूप इस प्रकार होना चाहिए", "WALLET_accountNo_hint_TR": "कृपया अपने वॉलेट से जुड़ा फ़ोन नंबर, 10 अंक या PL प्लस 10 अंक दर्ज करें,", "WALLET_accountNo_hint_PH": "कृपया अपने वॉलेट से जुड़ा फ़ोन नंबर दर्ज करें, 09 से शुरू होने वाले 11 अंक,", "WALLET_accountNo_hint_PH_LAZADAPH": "कृपया अपने वॉलेट से जुड़ा फ़ोन नंबर, 09 से शुरू होने वाले 11 अंक या ईमेल दर्ज करें,", "WALLET_accountNo_hint_BR_MERCADOPAGO": "कृपया वॉलेट से जुड़ा उपयोगकर्ता खाता (ईमेल या आईडी) दर्ज करें,", "WALLET_accountNo_hint_BR_PIX": "कृपया वॉलेट से जुड़ा संबंधित खाता दर्ज करें,", "WALLET_accountNo_hint_BR_PagBank": "कृपया अपने वॉलेट से जुड़ा ईमेल पता दर्ज करें, अधिकतम 60 अक्षर,", "WALLET_accountNo_hint_ID": "कृपया अपना वॉलेट-लिंक्ड खाता नंबर दर्ज करें: 08 से शुरू होने वाले 9 से 14 अंक", "WALLET_accountNo_hint_MY": "कृपया अपना वॉलेट-लिंक्ड खाता नंबर दर्ज करें: 0 से शुरू होने वाले 10 या 11 अंक", "WALLET_accountNo_hint_TH": "कृपया अपना वॉलेट-लिंक्ड खाता नंबर दर्ज करें: 0 से शुरू होने वाले 10 अंक", "WALLET_accountNo_hint_VN": "कृपया अपना वॉलेट-लिंक्ड खाता नंबर दर्ज करें: 84 से शुरू होने वाले 11 अंक", "WALLET_fullName_title": "भुगतानकर्ता का नाम,", "WALLET_fullName_placeholder": "उदाहरण के लिए एंटोनियो, माल्डोनाडो, इवेंजेलिस्टा", "WALLET_fullName_placeholder_AE": "उदाहरण: ली मिंग,", "WALLET_fullName_placeholder_PH": "उदा.एंटोनियो, माल्डोनाडो, इवेंजेलिस्टा,", "WALLET_fullName_placeholder_BR": "उदा.एंटोनियो माल्डोनाडो इवेंजेलिस्टा,", "WALLET_fullName_hint": "कृपया अपना पूरा अंग्रेजी नाम दर्ज करें,", "WALLET_lastName_title": "भुगतानकर्ता का अंतिम नाम,", "WALLET_lastName_placeholder_EG": "उदाहरण: इवेंजेलिस्टा,", "WALLET_lastName_hint": "कृपया अपना अंतिम नाम दर्ज करें,", "WALLET_accountType_title": "{0} बाइंडिंग खाता प्रकार,", "WALLET_accountType_placeholder_BR_PIX": "कृपया बाइंडिंग खाता प्रकार चुनें,", "WALLET_accountType_option_E_PIX": "ईमेल,", "WALLET_accountType_option_P_PIX": "फ़ोन ,", "WALLET_accountType_option_C_PIX": "सीपीएफ/सीएनपीजे (व्यक्तिगत/कॉर्पोरेट टैक्स नंबर),", "WALLET_accountType_option_B_PIX": "ईवीपी,", "WALLET_accountNo_placeholder_BR_PIX_B": "उदाहरण: 123e4567-e89b-12d3-a456-************,", "SWIFT_accountNo_title": "स्विफ्ट खाता,", "SWIFT_accountNo_placeholder_SA": "उदाहरण: ************************,", "SWIFT_accountNo_placeholder_AE": "उदाहरण: ***********************,", "SWIFT_accountNo_placeholder_KW": "उदाहरण: ******************************,", "SWIFT_accountNo_placeholder_QA": "उदाहरण: *****************************,", "SWIFT_accountNo_placeholder_TR": "उदाहरण: TR32001000999990**********,", "SWIFT_accountNo_placeholder_BH": "उदाहरण: **********************,", "SWIFT_accountNo_placeholder_YE": "उदा.**********,", "SWIFT_accountNo_placeholder_IQ": "उदाहरण: ***********************,", "SWIFT_accountNo_placeholder_IL": "उदाहरण: ***********************,", "SWIFT_accountNo_placeholder_PS": "उदा.**********,", "SWIFT_accountNo_placeholder_AZ": "उदाहरण:AZ77VTBA0000000000**********,", "SWIFT_accountNo_placeholder_LB": "उदाहरण:****************************,", "SWIFT_accountNo_placeholder_MA": "उदा.**********,", "SWIFT_accountNo_placeholder_PH": "उदा.**********,", "SWIFT_accountNo_placeholder_BR": "उदाहरण: *****************************,", "SWIFT_accountNo_placeholder_EG": "उदाहरण:*****************************,", "SWIFT_accountNo_placeholder_JO": "例如******************************,", "SWIFT_accountNo_hint_SA": "कृपया अपना SWIFT खाता दर्ज करें, SA से शुरू होने वाले 24 अक्षर,", "SWIFT_accountNo_hint_AE": "कृपया अपना SWIFT खाता दर्ज करें, AE से शुरू होने वाले 23 अक्षर,", "SWIFT_accountNo_hint_KW": "कृपया अपना SWIFT खाता दर्ज करें, K<PERSON> से शुरू होने वाले 30 अक्षर,", "SWIFT_accountNo_hint_QA": "कृपया अपना SWIFT खाता दर्ज करें, QA से शुरू होने वाले 29 अक्षर,", "SWIFT_accountNo_hint_TR": "कृपया अपना SWIFT खाता दर्ज करें, TR से शुरू होने वाले 26 अक्षर,", "SWIFT_accountNo_hint_BH": "कृपया अपना SWIFT खाता दर्ज करें, BH से शुरू होने वाले 22 अक्षर,", "SWIFT_accountNo_hint_YE": "कृपया अपना SWIFT खाता दर्ज करें, 34 अंक और अक्षर या उससे कम,", "SWIFT_accountNo_hint_IQ": "कृपया अपना SWIFT खाता दर्ज करें, IQ से शुरू होने वाले 23 अक्षर,", "SWIFT_accountNo_hint_IL": "कृपया अपना SWIFT खाता दर्ज करें, IL से शुरू होने वाले 23 अक्षर,", "SWIFT_accountNo_hint_PS": "कृपया अपना SWIFT खाता दर्ज करें, 34 अंक और अक्षर या उससे कम,", "SWIFT_accountNo_hint_AZ": "कृपया अपना SWIFT खाता दर्ज करें, A<PERSON> से शुरू होने वाले 28 अक्षर,", "SWIFT_accountNo_hint_LB": "कृपया अपना SWIFT खाता दर्ज करें, LB से शुरू होने वाले 28 अक्षर,", "SWIFT_accountNo_hint_MA": "कृपया अपना SWIFT खाता दर्ज करें, 34 अंक और अक्षर या उससे कम,", "SWIFT_accountNo_hint_PH": "कृपया अपना SWIFT खाता दर्ज करें, 34 अंक और अक्षर या उससे कम,", "SWIFT_accountNo_hint_BR": "कृपया अपना SWIFT खाता दर्ज करें, BR से शुरू होने वाले 29 अक्षर,", "SWIFT_accountNo_hint_EG": "कृपया अपना SWIFT खाता दर्ज करें, E<PERSON> से शुरू होने वाले 29 अक्षर,", "SWIFT_accountNo_hint_JO": "कृपया अपना SWIFT खाता दर्ज करें, JO से शुरू होने वाले 30 अक्षर,", "SWIFT_fullName_title": "भुगतानकर्ता का नाम,", "SWIFT_fullName_placeholder": "उदाहरण: ली मिंग,", "SWIFT_fullName_hint": "कृपया अपना पूरा अंग्रेजी नाम दर्ज करें,", "SWIFT_bankCode_title": "बैंक नंबर (SWIFT कोड),", "SWIFT_bankCode_placeholder": "उदाहरण: SCBLHKHH,", "SWIFT_bankCode_hint": "कृपया बैंक कोड दर्ज करें, 8 या 11 अंक, संख्याएं और बड़े अक्षर स्वीकार्य हैं,", "CARD_accountNo_title": "बैंक खाता ,", "CARD_accountNo_placeholder_EG": "उदाहरण: ***********,", "CARD_accountNo_placeholder_TR": "उदाहरण: ****************,", "CARD_accountNo_hint_EG": "कृपया बैंक खाता दर्ज करें, लंबाई>=8,", "CARD_accountNo_hint_TR": "कृपया 16-अंकीय बैंक कार्ड नंबर दर्ज करें, केवल डेबिट कार्ड समर्थित हैं,", "CARD_fullName_title": "भुगतानकर्ता का नाम,", "CARD_fullName_placeholder": "उदा.एंटोनियो माल्डोनाडो इवेंजेलिस्टा,", "CARD_fullName_hint": "कृपया अपना पूरा अंग्रेजी नाम दर्ज करें,", "CARRIER_BILLING_accountNo_title": "फ़ोन नंबर,", "CARRIER_BILLING_accountNo_placeholder": "उदा.63**********,", "CARRIER_BILLING_accountNo_hint": "कृपया फ़ोन नंबर दर्ज करें।,", "CASH_accountNo_title": "{0}खाता,", "CASH_accountNo_placeholder": "उदा.***********,", "CASH_accountNo_hint": "कृपया{0}खाता दर्ज करें, 0 से शुरू होने वाले 11 अंक,", "BANK_TRANSFER_accountNo_title_SA": "बैंक खाता (IBAN),", "BANK_TRANSFER_accountNo_title_AE": "बैंक खाता (IBAN),", "BANK_TRANSFER_accountNo_title_TR": "बैंक खाता (IBAN),", "BANK_TRANSFER_accountNo_title": "बैंक खाता", "BANK_TRANSFER_accountNo_title_EG": "बैंक खाता,", "BANK_TRANSFER_accountNo_title_KW": "बैंक खाता,", "BANK_TRANSFER_accountNo_title_QA": "बैंक खाता,", "BANK_TRANSFER_accountNo_title_MA": "बैंक खाता (आरआईबी),", "BANK_TRANSFER_accountNo_title_PH": "बैंक खाता,", "BANK_TRANSFER_accountNo_title_BR": "बैंक खाता,", "BANK_TRANSFER_accountNo_placeholder_SA": "उदाहरण: ************************,", "BANK_TRANSFER_accountNo_placeholder_EG": "उदा.************,", "BANK_TRANSFER_accountNo_placeholder_AE": "उदाहरण: ***********************,", "BANK_TRANSFER_accountNo_placeholder_KW": "उदाहरण: ******************************,", "BANK_TRANSFER_accountNo_placeholder_QA": "उदाहरण: *****************************,", "BANK_TRANSFER_accountNo_placeholder_TR": "उदाहरण: **************************,", "BANK_TRANSFER_accountNo_placeholder_MA": "उदा.123456789876543212345678,", "BANK_TRANSFER_accountNo_placeholder_PH": "उदा.************,", "BANK_TRANSFER_accountNo_placeholder_BR": "उदाहरण:***********,", "BANK_TRANSFER_accountNo_placeholder_JO": "उदाहरण: ******************************,", "BANK_TRANSFER_accountNo_placeholder_MY": "उदाहरण: **********", "BANK_TRANSFER_accountNo_placeholder_TH": "उदाहरण: *********", "BANK_TRANSFER_accountNo_placeholder_ID": "उदाहरण: ***********", "BANK_TRANSFER_accountNo_hint_SA": "कृपया SA+22 अक्षरों और संख्याओं का संयोजन दर्ज करें,", "BANK_TRANSFER_accountNo_hint_EG": "कृपया IBAN और स्थानीय बैंक खाता दर्ज करें, IBAN; 29 अक्षर (EG+27 अंक),", "BANK_TRANSFER_accountNo_hint_AE": "कृपया AE+21 अंक दर्ज करें, AE बड़े अक्षरों में लिखा जाना चाहिए,", "BANK_TRANSFER_accountNo_hint_KW": "कृपया IBAN प्रारूप में 50 अक्षरों से कम लंबाई दर्ज करें,", "BANK_TRANSFER_accountNo_hint_QA": "कृपया IBAN प्रारूप में 50 अक्षरों से कम लंबाई दर्ज करें,", "BANK_TRANSFER_accountNo_hint_TR": "कृपया IBAN प्रारूप में TR से शुरू होने वाला 24 अंक वाला नंबर दर्ज करें,", "BANK_TRANSFER_accountNo_hint_MA": "कृपया 24-अंकीय RIB खाता कोड दर्ज करें।", "BANK_TRANSFER_accountNo_hint_PH": "कृपया 6 से 128 अंक दर्ज करें,", "BANK_TRANSFER_accountNo_hint_BR": "कृपया 4 से 15 अंक दर्ज करें,", "BANK_TRANSFER_accountNo_hint_JO": "कृपया JO से शुरू होने वाले 30 अक्षरों और संख्याओं का संयोजन दर्ज करें,", "BANK_TRANSFER_accountNo_hint_MY": "अधिकतम 35 अक्षरों वाला खाता नंबर दर्ज करें।", "BANK_TRANSFER_accountNo_hint_TH": "कृपया 10-18 अंकों का खाता नंबर दर्ज करें", "BANK_TRANSFER_accountNo_hint_ID": "संख्यात्मक खाता संख्या दर्ज करें.", "BANK_TRANSFER_bankBranch_title": "बैंक शाखा संख्या,", "BANK_TRANSFER_bankBranch_placeholder_BR": "उदाहरण:1234,", "BANK_TRANSFER_bankBranch_hint_BR": "कृपया बैंक शाखा संख्या दर्ज करें, 4~6 अंक,", "BANK_TRANSFER_address_placeholder_SA": "उदाहरण: aabbbcccccccc,", "BANK_TRANSFER_address_placeholder_AE": "उदा. एलिसी स्ट्रीट कादिकोय इस्तांबुल,", "BANK_TRANSFER_address_placeholder_KW": "उदाहरण: कुवैत xx स्ट्रीट,", "BANK_TRANSFER_address_hint_SA": "कृपया पता दर्ज करें, लंबाई सीमा 10 से 200 वर्णों तक है, केवल संख्याएं, पूर्ण विराम, बड़े और छोटे अक्षर, तथा रिक्त स्थान की अनुमति है।", "BANK_TRANSFER_address_hint_AE": "कृपया पता दर्ज करें, लंबाई सीमा 1 से 200 वर्णों तक है, बैंक जोखिम नियंत्रण जांच करेगा।", "BANK_TRANSFER_address_hint_KW": "कृपया अपना पता दर्ज करें, लंबाई<=255,", "BANK_TRANSFER_fullName_placeholder_TR": "उदाहरण: एंटोनियो,", "BANK_TRANSFER_fullName_placeholder_PH": "उदाहरण: एंटोनियो,", "BANK_TRANSFER_fullName_placeholder_BR": "उदाहरण: एंटोनियो,", "BANK_TRANSFER_fullName_placeholder_MA": "उदाहरण: जॉर्ज,", "BANK_TRANSFER_fullName_placeholder_KW": "उदाहरण: जॉर्ज किन,", "BANK_TRANSFER_phone_placeholder_PH": "उदा.***********,", "BANK_TRANSFER_phone_placeholder_BR": "उदाहरण:*************,", "BANK_TRANSFER_phone_hint_PH": "कृपया अपना मोबाइल फ़ोन नंबर दर्ज करें, 0-9 अंक 11 से शुरू (0 जोड़ना आवश्यक है),", "BANK_TRANSFER_phone_hint_BR": "कृपया अपना मोबाइल फ़ोन नंबर दर्ज करें, 55 से शुरू होने वाले 12-13 अंक,", "BANK_TRANSFER_bankCode_title_MY": "बैंक कोड (SWIFT कोड)", "BANK_TRANSFER_bankCode_placeholder_MY": "उदाहरणार्थ CITIHK0001", "BANK_TRANSFER_bankCode_hint_MY": "अक्षरों और संख्याओं का 8-11 वर्ण संयोजन दर्ज करें।", "BANK_TRANSFER_bankBranch_title_SA": "SWIFT कोड", "BANK_TRANSFER_bankBranch_placeholder_SA": "********", "BANK_TRANSFER_bankBranch_hint_SA": "8 या 11 बड़े अक्षरों और संख्याओं का संयोजन दर्ज करें", "BANK_TRANSFER_city_title_SA": "<PERSON><PERSON><PERSON>", "BANK_TRANSFER_city_placeholder_SA": "उदाहरण New York", "BANK_TRANSFER_city_hint_SA": "शहर का नाम दर्ज करें, अधिकतम 35 वर्ण", "DLOCAL_BANK_TRANSFER_accountNo_placeholder_MA": "उदाहरण: 001123211111111111111111", "DLOCAL_BANK_TRANSFER_accountNo_hint_MA": "24 अंकों का संयोजन दर्ज करें", "DLOCAL_BANK_TRANSFER_birthday_title_AE": "जन्म तारीख", "DLOCAL_BANK_TRANSFER_birthday_placeholder_AE": "उदाहरण ********", "DLOCAL_BANK_TRANSFER_birthday_hint_AE": "अपनी जन्मतिथि भरें", "DLOCAL_BANK_TRANSFER_documentType_title_AE": "दस्तावेज़ प्रकार", "DLOCAL_BANK_TRANSFER_documentType_placeholder_AE": "कृपया दस्तावेज़ प्रकार चुनें", "DLOCAL_BANK_TRANSFER_documentType_option_ID_AE": "ID", "DLOCAL_BANK_TRANSFER_documentType_option_PASS_AE": "पास", "DLOCAL_BANK_TRANSFER_documentId_title_AE": "दस्तावेज़ आईडी", "DLOCAL_BANK_TRANSFER_documentId_placeholder_ID_AE": "उदाहरण NNN-NNNN-NNNNNNN-N", "DLOCAL_BANK_TRANSFER_documentId_placeholder_PASS_AE": "उदाहरण ABCD1234", "DLOCAL_BANK_TRANSFER_documentId_hint_AE": "कृपया अपना आईडी दर्ज करें", "AGENT_accountNo_Whatsapp_title_ALL": "व्हाट्सएप नंबर,", "AGENT_accountNo_Whatsapp_placeholder_ALL": "उदाहरण के लिए +90 111 111 11 11,", "AGENT_accountNo_Whatsapp_hint_ALL": "कृपया अपना व्हाट्सएप अकाउंट दर्ज करें,", "AGENT_accountNo_vodafone_title_EG": "वोडाफोन खाता", "AGENT_fullName_title_ID": "भुगतान प्राप्तकर्ता आईडी", "AGENT_fullName_placeholder_ID": "उदाहरण: 001123211111111111111111", "AGENT_fullName_hint_ID": "कृपया भुगतान प्राप्तकर्ता आईडी दर्ज करें", "USDT_blockchain_title_ALL": "ब्लॉकचेन नाम,", "USDT_address_title_ALL": "सिक्का जारी करने का पता,", "USDT_address_placeholder_ALL": "उदाहरण के लिए 11111111111111111111111111,", "USDT_address_hint_ALL": "कृपया 1, 3, या bc से शुरू होने वाला पता दर्ज करें,", "USDT_address_hint_common": "कृपया अपना USDT पता दर्ज करें"}