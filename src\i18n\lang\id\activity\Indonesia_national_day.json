{"greeting": "Selamat Hari Kemerdekaan Indonesia!", "tasks": "Tugas", "activity_gifts": "Gift Event", "tasks_and_rankings_info": "Hanya 3 jenis gift event berikut yang dihitung untuk tugas dan peringkat:", "daily_tasks": "<PERSON><PERSON> harian", "daily_task_frequency": "Dapat diselesaikan satu kali per hari", "sign_in": "<PERSON><PERSON><PERSON>", "broadcast_2_hours": "Siaran di ruangmu sendiri selama 30 menit", "limited_tasks": "Tugas terbatas", "limited_task_frequency": "<PERSON><PERSON> dapat diselesaikan satu kali selama periode event", "give_20000_coins_gift": "Memberi gift event senilai 5000 koin kepada pengguna lain", "receive_50000_coins_gift": "Menerima gift event senilai 5000 berlian", "room_gift_flow_100000": "Total aliran gift event yang diterima di ruangmu mencapai 10000 berlian", "go": "<PERSON><PERSON><PERSON><PERSON>", "claim": "<PERSON><PERSON><PERSON>", "claimed": "<PERSON><PERSON>", "congratulations": "Selamat!", "task_reward_sent": "<PERSON><PERSON> tugas telah dikirim ke ranselmu", "ok": "OK", "rankings": "<PERSON><PERSON><PERSON>", "gift_receiving_ranking": "Peringkat Penerima Gift", "gift_receiving_ranking_info": "Peringkat ini menghitung jumlah berlian yang kamu terima dari 3 gift event. Pengguna TOP1–3 akan mendapatkan hadiah.", "gift_giving_ranking": "Peringkat Pemberi Gift", "gift_giving_ranking_info": "Peringkat ini menghitung jumlah koin yang kamu keluarkan untuk 3 gift event. Pengguna TOP1–3 akan mendapatkan hadiah.", "room_gift_ranking": "Pering<PERSON> Ruangan", "room_gift_ranking_info": "Peringkat ini menghitung jumlah berlian yang diterima di ruanganmu dari 3 gift. Pemilik ruangan TOP1–3 akan mendapatkan hadiah.", "rules_and_rewards": "Aturan & Hadiah", "rewards": "<PERSON><PERSON>", "task_rewards": "<PERSON><PERSON>", "gift_receiving_ranking_rewards": "<PERSON><PERSON>", "gift_giving_ranking_rewards": "<PERSON><PERSON>emberi <PERSON>", "top1": "TOP1", "top2": "TOP2", "top3": "TOP3", "room_gift_ranking_rewards": "<PERSON><PERSON>uang<PERSON>", "top1_room_owner": "Pemilik Ruangan TOP1", "top2_room_owner": "Pemilik Ruangan TOP2", "top3_room_owner": "Pemilik Ruangan TOP3", "rules": "<PERSON><PERSON><PERSON>", "rules_activity_time_label": "Durasi Event", "rules_activity_time": "Dari 11 Agustus 00:00:00 hingga 17 Agustus 23:59:59 (UTC+7)", "rules_activity_gifts_label": "Gift Event", "rules_activity_gifts": "Hanya 3 jenis gift event yang dihitung untuk tugas dan peringkat", "rules_tasks_label": "Tugas", "rules_tasks": "<PERSON><PERSON><PERSON> men<PERSON><PERSON><PERSON><PERSON> tugas, kamu harus kembali ke halaman event untuk meng<PERSON><PERSON> hadiah", "rules_daily_tasks_label": "<PERSON><PERSON> harian", "rules_daily_tasks": "Dapat diselesaikan satu kali per hari", "rules_limited_tasks_label": "Tugas terbatas", "rules_limited_tasks": "活动期间仅可Hanya dapat diselesaikan satu kali selama periode event完成一次", "rules_rankings_label": "<PERSON><PERSON><PERSON>", "rules_rankings": "<PERSON><PERSON> per<PERSON>kat akan dikirim ke ransel pengguna pemenang setelah event berakhir", "rules_gift_receiving_ranking_label": "Peringkat Penerima Gift", "rules_gift_receiving_ranking": "Peringkat ini menghitung jumlah berlian yang kamu terima dari 3 gift event. Pengguna TOP1–3 akan mendapatkan hadiah.", "rules_gift_giving_ranking_label": "Peringkat Pemberi Gift", "rules_gift_giving_ranking": "Peringkat ini menghitung total koin yang kamu berikan melalui 3 jenis gift event. Pengguna dengan peringkat TOP1–3 akan mendapatkan hadiah.", "rules_room_gift_ranking_label": "Pering<PERSON> Ruangan", "rules_room_gift_ranking": "Peringkat ini menghitung jumlah berlian yang diterima di ruanganmu dari 3 gift. Pemilik ruangan TOP1–3 akan mendapatkan hadiah.", "rules_feedback": "Jika kamu memiliki masukan mengenai event ini, silakan klik tombol di bagian atas halaman untuk memberikan saran berharga."}