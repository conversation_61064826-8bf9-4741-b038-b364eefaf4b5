{"title": "", "description": "", "registration_requirements_title": "", "registration_requirements_items_0": "", "registration_requirements_items_1": "", "registration_requirements_items_2": "", "registration_requirements_items_3": "", "submit_audio": "", "submit_success": "", "update_audio": "", "submission_limit_reached": "", "under_review": "", "registration_deadline": "", "registration_record": "", "no_data": "", "review_note": "", "submission_time": "", "my_work": "", "submission_status_title": "", "submission_status_awaiting_review": "", "submission_status_under_review": "", "submission_status_review_passed": "", "submission_status_review_failed": "", "submission_status_expired": "", "good_voice": "", "audition_round_title": "", "audition_round_voting": "", "audition_round_vote": "", "audition_round_did_not_participate": "", "audition_round_vote_success": "", "audition_round_cannot_vote_repeatedly": "", "audition_round_all_votes_used": "", "my_friends": "", "popular_ranking": "", "audition_to_qualifying": "", "random_list": "", "change_one": "", "qualifying_round_title": "", "qualifying_round_not_started": "", "qualifying_round_competition_room_title": "", "qualifying_round_competition_room_room_name": "", "qualifying_round_competition_room_room_id": "", "qualifying_round_competition_room_competition_time": "", "qualifying_round_competition_room_remind_me": "", "qualifying_round_competition_room_go": "", "qualifying_round_competition_room_ended": "", "qualifying_round_competition_room_notification": "", "qualifying_round_participants_list": "", "qualifying_round_to_final": "", "qualifying_round_ranking_title": "", "qualifying_round_ranking_scoring_method": "", "qualifying_round_ranking_total_ranking": "", "qualifying_round_ranking_avatar": "", "qualifying_round_ranking_nickname": "", "qualifying_round_ranking_gift_points": "", "qualifying_round_ranking_judges_scores": "", "final_round_title": "", "final_round_participants_list": "", "final_round_rewards": "", "final_round_ranking": "", "popularity_ranking_title": "", "popularity_ranking_description": "", "support_ranking_title": "", "support_ranking_description": "", "return": "", "rules_title": "", "rules_activity_time_title": "", "rules_activity_time_registration": "", "rules_activity_time_official_review": "", "rules_activity_time_audition_round": "", "rules_activity_time_qualifying_round": "", "rules_activity_time_final_round": "", "rules_audition_round_details_title": "", "rules_audition_round_details_items_0": "", "rules_audition_round_details_items_1": "", "rules_qualifying_round_details_title": "", "rules_qualifying_round_details_items_0": "", "rules_qualifying_round_details_items_1": "", "rules_qualifying_round_details_items_2": "", "rules_final_round_details_title": "", "rules_final_round_details_items_0": "", "rules_final_round_details_items_1": "", "rules_final_round_details_items_2": "", "rules_final_round_details_items_3": "", "rules_rankings_title": "", "rules_rankings_popularity_ranking": "", "rules_rankings_support_ranking": "", "rules_reward_distribution_title": "", "rules_reward_distribution_items_0": "", "rules_reward_distribution_items_1": "", "rules_rewards_title": "", "rules_rewards_competition_rewards_title": "", "rules_rewards_competition_rewards_audition_top20": "", "rules_rewards_competition_rewards_qualifying_top10": "", "rules_rewards_competition_rewards_final_rewards_title": "", "rules_rewards_competition_rewards_final_rewards_top1": "", "rules_rewards_competition_rewards_final_rewards_top2": "", "rules_rewards_competition_rewards_final_rewards_top3": "", "rules_rewards_competition_rewards_final_rewards_others": "", "rules_rewards_rankings_rewards_title": "", "rules_rewards_rankings_rewards_popularity_top1": "", "rules_rewards_rankings_rewards_support_top1": "", "loading": "", "time": "", "judges_scores_title": "", "judges_scores_description": "", "gift_points_title": "", "gift_points_description": "", "total_score_title": "", "total_score_description": ""}