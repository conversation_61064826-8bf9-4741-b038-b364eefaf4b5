<template>
  <div class="cp-container">
    <right-button :text="$t('stellar_voyage.rules')"></right-button>
    <task-button></task-button>
    <div class="bg-box">
      <header-bar
        back-icon-show
        :padding="false"
        :content-padding="false"
        :show-title="false"
        close-type="close"
      />

      <div
        class="banner-title"
        :style="{
          backgroundImage: `url('${getImageUrl('title.png')}')`,
        }"
      >
        <img :src="getImageUrl('title.png')" draggable="false" />
      </div>

      <div class="time">
        <TitleBlock
          title="08/18 - 08/31"
          :font-size="14"
          :min-width="0"
        ></TitleBlock>
      </div>
    </div>
    <div class="content">
      <div class="block-tabs">
        <BlockTabs v-model:current="currentTab"></BlockTabs>
      </div>
      <div class="block-boos">
        <div v-if="currentTab !== 1" class="box1">
          <BlockBox>
            <div class="block-box-content">
              <div class="title-block-box">
                <TitleBlockBox title="Activity Gifts"></TitleBlockBox>
              </div>
              <div class="rewards">
                <template v-for="(item, index) in 10">
                  <gap v-if="index !== 0" :gap="16"></gap>
                  <div class="reward-item">
                    <div class="cover">
                      <PrizeBox :rewards="{}"></PrizeBox>
                    </div>
                    <div class="value-box">
                      <div class="icon-heart"></div>
                      <div>x</div>
                      <div>1</div>
                    </div>
                  </div>
                </template>
              </div>
              <div class="activity-gifts-desc">
                Giving the above gifts will count the
              </div>
            </div>
          </BlockBox>
        </div>
        <div v-if="currentTab === 0" class="box2">
          <BlockBox>
            <div class="block-box-content">
              <div class="rewards-btn">
                <RewardsButton></RewardsButton>
              </div>
              <div class="title-block-box">
                <TitleBlockBox title="Gift Play Rules"></TitleBlockBox>
              </div>
              <div class="sub-title">
                <TitleBlockSub title="Mode 1"></TitleBlockSub>
              </div>
              <div class="sub-desc">
                1.Receive 1 ，3，5 gifts for the first time each day to get {}
                2.Receive 2，3，4 gifts for the first time each day to get {}
              </div>
              <div class="lock-rewords">
                <div class="rewords-row">
                  <div v-for="(item, index) in 3" class="lock-reward-item">
                    <div class="lock-cover">
                      <PrizeBox :rewards="{}"></PrizeBox>
                    </div>
                  </div>
                  <div class="to"></div>
                  <div class="lock-reward-item">
                    <div class="lock-cover">
                      <PrizeBox :rewards="{}"></PrizeBox>
                    </div>
                  </div>
                </div>
                <div class="rewords-row">
                  <div v-for="(item, index) in 3" class="lock-reward-item">
                    <div class="lock-cover">
                      <PrizeBox :rewards="{}"></PrizeBox>
                    </div>
                  </div>
                  <div class="to"></div>
                  <div class="lock-reward-item">
                    <div class="lock-cover">
                      <PrizeBox :rewards="{}"></PrizeBox>
                    </div>
                  </div>
                </div>
              </div>
              <div class="sub-desc mt0">
                <span>
                  Receive all of the gifts for the first time each day to get
                </span>
                <img
                  src="@/assets/images/activity/cp-paradise/ticket.png"
                  class="ticket"
                />
                <span>x</span>
                <span>1</span>
              </div>
              <div class="sub-title">
                <TitleBlockSub title="Mode 2"></TitleBlockSub>
              </div>
              <div class="task-list">
                <div class="task-item" v-for="i in 2">
                  <div class="task-cover">
                    <PrizeBox :rewards="{}"></PrizeBox>
                  </div>
                  <gap :gap="10"></gap>
                  <div class="task-content">
                    <div class="task-title">
                      Receive 99 {Rings} gifts to get
                    </div>
                    <div class="task-process">
                      <Process :max="99" :current="12"></Process>
                    </div>
                  </div>
                </div>
              </div>
              <div class="sub-title">
                <TitleBlockSub title="Mode 3"></TitleBlockSub>
              </div>
              <div class="sub-desc">
                Each time you send 1 {Ocean Wedding} gift, you have a chance to
                get {}. If you haven’t received it after sending 9, the 10th
                send will get {} for sure.
              </div>
              <div class="send-rewards">
                <div class="send-reward">
                  <PrizeBox :rewards="{}"></PrizeBox>
                </div>
                <gap :gap="12"></gap>
                <div class="to"></div>
                <gap :gap="12"></gap>
                <div class="send-reward">
                  <PrizeBox :rewards="{}"></PrizeBox>
                </div>
              </div>
              <div class="sub-desc">
                Send at most 7 more, you will definitely get
              </div>
            </div>
          </BlockBox>
        </div>
        <div v-if="currentTab === 1" class="box3">
          <div>
            <BlockSubTabs v-model:current="currentPoolTab"></BlockSubTabs>
          </div>
          <div class="desc">
            Spend coins to draw prizes, 100% chance of winning, lots of
            high-value gifts waiting for you to draw!
          </div>
          <div class="notify"></div>
          <div class="wheel">
            <Wheel></Wheel>
          </div>
        </div>
        <div v-if="currentTab === 2" class="box4">
          <Ranking></Ranking>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import RightButton from "./components/RightButton.vue";
import TaskButton from "./components/TaskButton.vue";
import TitleBlock from "./components/TitleBlock.vue";
import BlockTabs from "./components/BlockTabs.vue";
import BlockSubTabs from "./components/BlockSubTabs.vue";
import BlockBox from "./components/BlockBox.vue";
import PrizeBox from "./components/PrizeBox.vue";
import TitleBlockSub from "./components/TitleBlockSub.vue";
import TitleBlockBox from "./components/TitleBlockBox.vue";
import Process from "./components/Process.vue";
import RewardsButton from "./components/RewardsButton.vue";
import Ranking from "./components/Ranking.vue";
import Wheel from "./components/Wheel.vue";

import { getLang } from "@/i18n/index.js";
import { ref } from "vue";
const currentTab = ref(0);
const currentPoolTab = ref(0);
const lang = getLang();
function getImageUrl(name) {
  return new URL(
    `../../../assets/images/activity/cp-paradise/${lang}/${name}`,
    import.meta.url
  ).href;
}
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.cp-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #b399ff 38.21%, #8462ff 100%);
  overflow: hidden;
  padding-bottom: px2rem(20);
  .bg-box {
    overflow: hidden;
    width: 100%;
    height: px2rem(428);
    background: url("@/assets/images/activity/cp-paradise/bg.jpg") no-repeat;
    background-size: 100% 100%;

    .banner-title {
      width: fit-content;
      height: fit-content;
      background-size: 100% 100%;
      margin: 0 auto;
      margin-top: px2rem(58);
      img {
        opacity: 0;
        width: px2rem(270);
        height: px2rem(72);
        vertical-align: bottom;
      }
    }

    .time {
      width: px2rem(146);
      margin: 0 auto;
      margin-top: px2rem(-12);
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: px2rem(-60);
    .block-boos {
      width: 100%;

      .block-box-content {
        padding: px2rem(48) 0;
        position: relative;
        .rewards-btn {
          position: absolute;
          top: px2rem(40);
          right: px2rem(30);
          z-index: 4;
        }
        .rewards {
          display: flex;
          flex-wrap: nowrap;
          overflow-x: auto;
          width: px2rem(304);
          margin: 0 auto;
          margin-top: px2rem(13);
          z-index: 3;
          .reward-item {
            position: relative;
            width: px2rem(58);
            &::after {
              content: "";
              position: absolute;
              z-index: 2;
              inset: 0;
            }
            .cover {
              width: px2rem(58);
              height: px2rem(58);
            }

            .value-box {
              display: flex;
              align-items: center;
              justify-content: center;

              color: #fff;
              text-align: center;
              font-family: Gilroy-Medium;
              font-size: px2rem(12);
              font-style: normal;
              font-weight: 400;
              line-height: normal;
              margin-top: px2rem(10);

              .icon-heart {
                width: px2rem(20);
                height: px2rem(20);
                background-size: 100% 100%;
                background-image: url("@/assets/images/activity/cp-paradise/heart.png");
              }
            }
          }
        }

        .activity-gifts-desc {
          color: #d0c1ff;
          text-align: center;
          font-family: Gilroy;
          font-size: px2rem(12);
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          margin: 0 auto;
          margin-top: px2rem(15);
          width: px2rem(296);
        }

        .sub-title {
          margin: 0 auto;
          width: fit-content;
          margin-top: px2rem(14);
        }

        .sub-desc {
          color: #d0c1ff;
          text-align: center;
          font-family: Gilroy;
          font-size: px2rem(11);
          font-style: normal;
          font-weight: 500;
          line-height: normal;
          margin: 0 auto;
          margin-top: px2rem(15);
          width: px2rem(320);

          .ticket {
            width: px2rem(30);
            height: px2rem(30);
            flex-shrink: 0;
            display: inline;
            vertical-align: middle;
          }
        }

        .mt0 {
          margin-top: 0;
        }

        .lock-rewords {
          margin: 0 auto;
          margin-top: px2rem(12);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          .rewords-row {
            display: flex;
            align-items: center;
            justify-content: center;

            .lock-reward-item {
              margin: 0 px2rem(4);
              margin-bottom: px2rem(12);
              .lock-cover {
                width: px2rem(66);
                height: px2rem(66);
              }
            }

            .to {
              width: px2rem(20);
              height: px2rem(24);
              background: url("@/assets/images/activity/cp-paradise/to.png");
              background-size: 100% 100%;
            }
          }
        }

        .task-list {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-top: px2rem(12);
          .task-item {
            display: flex;
            align-items: center;
            padding: px2rem(9);
            width: px2rem(328);
            height: px2rem(68);
            border-radius: px2rem(9);
            border: 0.5px solid #fff;
            background: #7a4ffc;
            margin-bottom: px2rem(10);
            .task-cover {
              width: px2rem(50);
              height: px2rem(50);
            }
            .task-content {
              flex-grow: 1;
              .task-title {
                color: #f5e8ff;
                font-family: Gilroy;
                font-size: px2rem(12);
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                letter-spacing: -0.12px;
              }
              .task-process {
                margin-top: px2rem(10);
              }
            }
          }
        }
        .send-rewards {
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto;
          margin-top: px2rem(12);
          .send-reward {
            width: px2rem(72);
            height: px2rem(72);
          }
          .to {
            width: px2rem(20);
            height: px2rem(24);
            background: url("@/assets/images/activity/cp-paradise/to.png");
            background-size: 100% 100%;
          }
        }
      }

      .box3 {
        background-color: #b299ff;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: px2rem(9);

        .desc {
          padding-top: px2rem(19);
          width: px2rem(295);
          color: #fff;
          text-align: center;
          font-family: Gilroy-Regular;
          font-size: px2rem(12);
          font-style: normal;
          font-weight: 400;
          line-height: 128%; /* 15.36px */
        }

        .wheel {
          margin-top: px2rem(14);
        }
      }
    }
  }
}
</style>
