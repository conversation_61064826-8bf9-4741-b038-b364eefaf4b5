<template>
  <div class="block--box-wrapper">
    <div class="block--box-bg">
      <div></div>
    </div>
    <div class="block--box-content">
      <slot></slot>
    </div>
  </div>
</template>
<script setup></script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.block--box-wrapper {
  position: relative;
  width: 100%;
  min-height: px2rem(252);
  height: fit-content;
  .block--box-bg {
    position: absolute;
    inset: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    &::before {
      content: "";
      width: 100%;
      height: px2rem(126);
      background-image: url("@/assets/images/activity/cp-paradise/bg2.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      flex-shrink: 0;
    }
    div {
      width: 100%;
      flex-grow: 1;
      background-image: url("@/assets/images/activity/cp-paradise/bg3.png");
      background-size: 100% px2rem(126);
      background-repeat: repeat-y;
      margin: -1px 0;
    }
    &::after {
      content: "";
      width: 100%;
      height: px2rem(126);
      background-image: url("@/assets/images/activity/cp-paradise/bg4.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      flex-shrink: 0;
    }
  }

  .block--box-content {
    position: relative;
    z-index: 1;
  }
}
</style>
