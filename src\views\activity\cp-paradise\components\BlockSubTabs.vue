<template>
  <div class="block-sub-tabs-wrapper">
    <div class="block-tabs-bg"></div>
    <div class="block-tabs">
      <div
        class="block-tabs-item"
        :class="{ active: activeTab === index }"
        v-for="(item, index) in tabs"
        :key="item.label"
        @click="activeTab = index"
      >
        <span>{{ item.label }}</span>
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed } from "vue";

const emit = defineEmits(["update:current"]);
const props = defineProps({
  current: {
    type: Number,
    default: 0,
  },
  tabs: {
    type: Array,
    default: () => [{ label: "tab1" }, { label: "tab2" }],
  },
});

const activeTab = computed({
  get() {
    return props.current;
  },
  set(value) {
    emit("update:current", value);
  },
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.block-sub-tabs-wrapper {
  height: px2rem(36);
  width: px2rem(251);
  position: relative;
}
.block-tabs-bg {
  width: px2rem(251);
  height: px2rem(36);
  border-radius: px2rem(36);
  border: px2rem(0.4) solid rgba(102, 68, 255, 0.6);
  background: #8e62ff;
  position: absolute;
  left: px2rem(0);
  top: px2rem(0);
}
.block-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: px2rem(3) px2rem(4);
  width: px2rem(251);
  height: px2rem(36);

  .block-tabs-item {
    flex: 1;
    width: px2rem(120);
    height: px2rem(30);
    text-align: center;
    color: #e0c5ff;
    font-family: "DIN Next W1G";
    font-size: px2rem(13);
    font-style: normal;
    font-weight: 500;
    line-height: px2rem(11);
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;

    span {
      position: relative;
      z-index: 2;
    }
  }

  .active {
    color: #fff;
    font-weight: 700;
    &::after {
      content: "";
      width: px2rem(120);
      height: px2rem(26);
      position: absolute;
      top: px2rem(0);
      left: px2rem(0);

      border-radius: px2rem(30);
      border: px2rem(2) solid #fde3cd;
      background: linear-gradient(
          180deg,
          rgba(255, 255, 255, 0) 0%,
          rgba(255, 255, 255, 0.5) 29.55%,
          rgba(255, 255, 255, 0) 57.55%
        ),
        linear-gradient(180deg, #f364ff 0%, #ff4fe5 47.12%, #ff9efd 100%);
      box-shadow: 0 -4px 4px 0 rgba(255, 255, 255, 0.25) inset,
        0 -6px 4px 0 #fc46ff inset, -1px -15px 11.3px 0 #fd55ff inset,
        0 0 5.4px 0 rgba(255, 255, 255, 0.6) inset;
    }
  }
}
</style>
