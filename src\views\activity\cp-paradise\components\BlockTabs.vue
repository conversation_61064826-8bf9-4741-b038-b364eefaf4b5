<template>
  <div class="block-tabs-wrapper">
    <div class="block-tabs-bg"></div>
    <div class="block-tabs">
      <div
        class="block-tabs-item"
        :class="{ active: activeTab === index }"
        v-for="(item, index) in tabs"
        :key="item.label"
        @click="activeTab = index"
      >
        <span>{{ item.label }}</span>
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed } from "vue";

const emit = defineEmits(["update:current"]);
const props = defineProps({
  current: {
    type: Number,
    default: 0,
  },
  tabs: {
    type: Array,
    default: () => [{ label: "tab1" }, { label: "tab2" }, { label: "tab2" }],
  },
});

const activeTab = computed({
  get() {
    return props.current;
  },
  set(value) {
    emit("update:current", value);
  },
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.block-tabs-wrapper {
  height: px2rem(44);
  width: px2rem(372);
  position: relative;
}
.block-tabs-bg {
  width: px2rem(348);
  height: px2rem(30);
  border-radius: px2rem(15);
  border: px2rem(0.4) solid rgba(102, 68, 255, 0.6);
  background: #8e62ff;
  position: absolute;
  left: px2rem(12);
  top: px2rem(3);
}
.block-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .block-tabs-item {
    flex: 1;
    width: px2rem(123);
    height: px2rem(43);
    padding-top: px2rem(13);
    text-align: center;
    color: #e0c5ff;
    font-family: "DIN Next W1G";
    font-size: px2rem(11);
    font-style: normal;
    font-weight: 500;
    line-height: px2rem(11);
    position: relative;

    span {
      position: relative;
      z-index: 2;
    }
  }

  .active {
    color: #fff;
    font-weight: 700;
    &::after {
      content: "";
      width: px2rem(123);
      height: px2rem(43);
      position: absolute;
      top: px2rem(0);
      left: px2rem(0);
      background: url("@/assets/images/activity/cp-paradise/active-tab.png");
      background-size: 100% 100%;
    }
  }
}
</style>
