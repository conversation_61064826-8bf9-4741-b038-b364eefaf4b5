<template>
  <van-overlay
    :show="showPopup"
    :lock-scroll="false"
    z-index="10002"
    @click="showPopup = false"
  >
    <div class="base-modal-wrapper" @click.stop="">
      <div class="title">
        <TitleBlockPopup title="My Partner"></TitleBlockPopup>
      </div>
      <div class="rank-list">
        <div class="rank-item" v-for="i in 10">
          <gap :gap="20"></gap>
          <div class="rank">
            <div class="top1" v-if="i === 1"></div>
            <div class="top2" v-else-if="i === 2"></div>
            <div class="top3" v-else-if="i === 3"></div>
            <div v-else>4</div>
          </div>
          <gap :gap="20"></gap>
          <div class="cp">
            <div class="user">
              <div class="avatar">
                <frame-avatar
                  :avatar="''"
                  :frame="null"
                  :size="46"
                ></frame-avatar>
              </div>
              <div class="nickname"><PERSON></div>
            </div>
            <gap :gap="20"></gap>
            <div class="cp-heart"></div>
            <div class="user">
              <div class="avatar">
                <frame-avatar
                  :avatar="''"
                  :frame="null"
                  :size="46"
                ></frame-avatar>
              </div>
              <div class="nickname">Ronald Ronald</div>
            </div>
          </div>
          <gap :gap="15"></gap>
          <div class="value-box">
            <div class="value-icon"></div>
            <div class="value-number">13123</div>
          </div>
        </div>
      </div>
    </div>
  </van-overlay>
</template>
<script setup>
import { ref } from "vue";
import TitleBlockPopup from "./TitleBlockPopup.vue";

const showPopup = ref(false);
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.base-modal-wrapper {
  width: 100%;
  height: px2rem(492);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: fixed;
  bottom: 0;
  background-image: url("@/assets/images/activity/cp-paradise/popup-bg.png");
  background-size: 100% 100%;

  .title {
    margin-top: px2rem(-5);
  }

  .rank-list {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: px2rem(27);
    margin-top: px2rem(17);
    flex-grow: 1;
    overflow-y: auto;
    .rank-item {
      width: px2rem(330);
      height: px2rem(66);
      flex-shrink: 0;
      border-radius: px2rem(12);
      border: px2rem(0.6) solid #fff691;
      background: #f481ff;
      margin-bottom: px2rem(30);

      display: flex;
      align-items: center;

      .rank {
        width: px2rem(34);
        height: px2rem(36);
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        text-align: center;
        text-shadow: 0 0 4px rgba(255, 255, 255, 0.34);
        -webkit-text-stroke-width: 1.04px;
        -webkit-text-stroke-color: #000;
        font-family: "DIN Next LT Pro";
        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;

        .top1,
        .top2,
        .top3 {
          width: px2rem(33);
          height: px2rem(36);
          flex-shrink: 0;
          background-size: 100% 100%;
        }
        .top1 {
          background-image: url("@/assets/images/activity/cp-paradise/top1.png");
        }
        .top2 {
          background-image: url("@/assets/images/activity/cp-paradise/top2.png");
        }
        .top3 {
          background-image: url("@/assets/images/activity/cp-paradise/top3.png");
        }
      }

      .cp {
        display: flex;
        position: relative;
        margin-top: px2rem(-16);
        .user {
          width: px2rem(80);
          display: flex;
          flex-direction: column;
          align-items: center;
          .nickname {
            margin-top: px2rem(5);
            color: #fff;
            font-family: Gilroy-Medium;
            font-size: px2rem(11);
            font-style: normal;
            font-weight: 400;
            line-height: 124%; /* 16.12px */
          }
        }
        .cp-heart {
          width: px2rem(38);
          height: px2rem(38);
          flex-shrink: 0;
          background-image: url("@/assets/images/activity/cp-paradise/cp-heart.png");
          background-size: px2rem(38) px2rem(38);
          background-repeat: no-repeat;

          position: absolute;
          left: 50%;
          transform: translateX(-50%);
        }
      }

      .value-box {
        width: px2rem(58);
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;

        .value-icon {
          width: px2rem(18);
          height: px2rem(18);
          background-image: url("@/assets/images/activity/cp-paradise/heart.png");
          background-size: 100% 100%;
        }

        .value-number {
          color: #fff;
          text-shadow: 0 0 4.252px #fa3ebb;
          font-family: "DIN Next W1G";
          font-size: px2rem(12);
          font-style: normal;
          font-weight: 500;
          line-height: 100%; /* 12px */
        }
      }
    }
  }
}
</style>
