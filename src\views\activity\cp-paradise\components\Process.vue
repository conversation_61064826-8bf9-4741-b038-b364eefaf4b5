<template>
  <div class="process-wrapper">
    <div class="p-box">
      <div class="inner" :style="{ width: width }"></div>
    </div>
    <gap :gap="6"></gap>
    <div class="process-text">{{ `(${current}/${max})` }}</div>
  </div>
</template>
<script setup>
import { computed } from "vue";

const props = defineProps({
  max: {
    type: Number,
    default: 100,
  },
  current: {
    type: Number,
    default: 0,
  },
});

const width = computed(() => `${(props.current / props.max) * 100}%`);
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.process-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}
.p-box {
  flex-grow: 1;
  height: px2rem(8);
  border-radius: px2rem(4);
  border: px2rem(1) solid rgba(255, 255, 255, 0.08);
  background: rgba(255, 255, 255, 0.08);
  position: relative;
  .inner {
    position: absolute;
    top: px2rem(-1);
    left: 0;
    height: px2rem(8);
    background: linear-gradient(90deg, #ff56f7 0%, #ff9be3 100%);
    border-radius: px2rem(4);
  }
}

.process-text {
  color: #f5e8ff;
  font-family: Gilroy;
  font-size: px2rem(11);
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.rtl-html {
  .p-box {
    .inner {
      left: unset;
      right: 0;
    }
  }
}
</style>
