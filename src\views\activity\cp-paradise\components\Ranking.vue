<template>
  <div class="ranking">
    <BlockBox>
      <div class="block-box-content">
        <div class="title-block-box">
          <TitleBlockBox title="CP ranking"></TitleBlockBox>
        </div>
        <div class="count-down">
          <CountDown></CountDown>
        </div>
        <div class="my-rank">
          <div class="user">
            <div class="avatar">
              <frame-avatar
                :avatar="''"
                :frame="null"
                :size="52"
              ></frame-avatar>
            </div>
            <div class="nickname"><PERSON></div>
          </div>
          <gap :gap="17"></gap>
          <div class="cp-heart">
            <div class="rank">TOP 1</div>
            <div class="value-box">
              <div class="value-icon"></div>
              <div class="value-number">13123</div>
            </div>
          </div>
          <gap :gap="17"></gap>
          <div class="user">
            <div v-if="false" class="avatar">
              <frame-avatar
                :avatar="''"
                :frame="null"
                :size="52"
              ></frame-avatar>
            </div>
            <div v-else class="invite"></div>
          </div>
        </div>
        <div class="rank-list">
          <div class="rank-item" v-for="i in 10">
            <gap :gap="20"></gap>
            <div class="rank">
              <div class="top1" v-if="i === 1"></div>
              <div class="top2" v-else-if="i === 2"></div>
              <div class="top3" v-else-if="i === 3"></div>
              <div v-else>4</div>
            </div>
            <gap :gap="20"></gap>
            <div class="cp">
              <div class="user">
                <div class="avatar">
                  <frame-avatar
                    :avatar="''"
                    :frame="null"
                    :size="46"
                  ></frame-avatar>
                </div>
                <div class="nickname">Ronald Ronald</div>
              </div>
              <gap :gap="20"></gap>
              <div class="cp-heart"></div>
              <div class="user">
                <div class="avatar">
                  <frame-avatar
                    :avatar="''"
                    :frame="null"
                    :size="46"
                  ></frame-avatar>
                </div>
                <div class="nickname">Ronald Ronald</div>
              </div>
            </div>
            <gap :gap="15"></gap>
            <div class="value-box">
              <div class="value-icon"></div>
              <div class="value-number">13123</div>
            </div>
          </div>
        </div>
      </div>
    </BlockBox>
    <MyPartnerPopup></MyPartnerPopup>
  </div>
</template>
<script setup>
import TitleBlockBox from "./TitleBlockBox.vue";
import BlockBox from "./BlockBox.vue";
import CountDown from "./CountDown.vue";
import MyPartnerPopup from "./MyPartnerPopup.vue";
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.block-box-content {
  overflow: hidden;
  padding-bottom: px2rem(48);
  .title-block-box {
    margin-top: px2rem(48);
  }
  .count-down {
    margin-top: px2rem(10);
  }
  .my-rank {
    width: px2rem(298);
    height: px2rem(116);
    margin: 0 auto;
    margin-top: px2rem(16);
    padding-top: px2rem(8);
    flex-shrink: 0;
    background-image: url("@/assets/images/activity/cp-paradise/my-rank-bg.png");
    background-size: 100% px2rem(73);
    background-repeat: no-repeat;
    background-position: bottom center;
    display: flex;
    justify-content: center;

    .user {
      width: px2rem(52);
      .avatar {
        margin-top: px2rem(0);
      }
      .nickname {
        margin-top: px2rem(5);
        color: #fff;
        font-family: Gilroy-Medium;
        font-size: px2rem(13);
        font-style: normal;
        font-weight: 400;
        line-height: 124%; /* 16.12px */
      }
      .invite {
        width: px2rem(52);
        height: px2rem(52);
        flex-shrink: 0;
        background-image: url("@/assets/images/activity/cp-paradise/invite.png");
        background-size: 100% 100%;
      }
    }

    .cp-heart {
      width: px2rem(55);
      height: px2rem(60);
      flex-shrink: 0;
      background-image: url("@/assets/images/activity/cp-paradise/cp-heart.png");
      background-size: px2rem(55) px2rem(55);
      background-repeat: no-repeat;
      .rank {
        margin-top: px2rem(30);
        color: #fff;
        text-align: center;
        text-shadow: 0 0 px2rem(12) rgba(219, 68, 212, 0.33);
        font-family: Gilroy;
        font-size: px2rem(13);
        font-style: normal;
        font-weight: 900;
        line-height: normal;
        text-transform: uppercase;
      }

      .value-box {
        width: px2rem(58);
        height: px2rem(18);
        flex-shrink: 0;
        border-radius: px2rem(58);
        border: px2rem(0.5) solid rgba(255, 255, 255, 0.5);
        background: #ff5ee4;
        display: flex;
        align-items: center;
        justify-content: center;

        .value-icon {
          width: px2rem(12);
          height: px2rem(10);
          background-image: url("@/assets/images/activity/cp-paradise/heart.png");
          background-size: 100% 100%;
        }

        .value-number {
          color: #fff;
          text-shadow: 0 0 4.252px #fa3ebb;
          font-family: "DIN Next W1G";
          font-size: px2rem(12);
          font-style: normal;
          font-weight: 500;
          line-height: 100%; /* 12px */
        }
      }
    }
  }

  .rank-list {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: px2rem(16);
    .rank-item {
      width: px2rem(330);
      height: px2rem(66);
      flex-shrink: 0;
      border-radius: px2rem(12);
      border: px2rem(0.6) solid #fff691;
      background: #f481ff;
      margin-bottom: px2rem(30);

      display: flex;
      align-items: center;

      .rank {
        width: px2rem(34);
        height: px2rem(36);
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        text-align: center;
        text-shadow: 0 0 4px rgba(255, 255, 255, 0.34);
        -webkit-text-stroke-width: 1.04px;
        -webkit-text-stroke-color: #000;
        font-family: "DIN Next LT Pro";
        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;

        .top1,
        .top2,
        .top3 {
          width: px2rem(33);
          height: px2rem(36);
          flex-shrink: 0;
          background-size: 100% 100%;
        }
        .top1 {
          background-image: url("@/assets/images/activity/cp-paradise/top1.png");
        }
        .top2 {
          background-image: url("@/assets/images/activity/cp-paradise/top2.png");
        }
        .top3 {
          background-image: url("@/assets/images/activity/cp-paradise/top3.png");
        }
      }

      .cp {
        display: flex;
        position: relative;
        margin-top: px2rem(-16);
        .user {
          width: px2rem(80);
          display: flex;
          flex-direction: column;
          align-items: center;
          .nickname {
            margin-top: px2rem(5);
            color: #fff;
            font-family: Gilroy-Medium;
            font-size: px2rem(11);
            font-style: normal;
            font-weight: 400;
            line-height: 124%; /* 16.12px */
          }
        }
        .cp-heart {
          width: px2rem(38);
          height: px2rem(38);
          flex-shrink: 0;
          background-image: url("@/assets/images/activity/cp-paradise/cp-heart.png");
          background-size: px2rem(38) px2rem(38);
          background-repeat: no-repeat;

          position: absolute;
          left: 50%;
          transform: translateX(-50%);
        }
      }

      .value-box {
        width: px2rem(58);
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;

        .value-icon {
          width: px2rem(18);
          height: px2rem(18);
          background-image: url("@/assets/images/activity/cp-paradise/heart.png");
          background-size: 100% 100%;
        }

        .value-number {
          color: #fff;
          text-shadow: 0 0 4.252px #fa3ebb;
          font-family: "DIN Next W1G";
          font-size: px2rem(12);
          font-style: normal;
          font-weight: 500;
          line-height: 100%; /* 12px */
        }
      }
    }
  }
}
</style>
