<template>
  <div class="rewards-button"></div>
  <RewardsDialog></RewardsDialog>
</template>
<script setup>
import RewardsDialog from "./RewardsDialog.vue";
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.rewards-button {
  width: px2rem(45);
  height: px2rem(45);
  flex-shrink: 0;
  background: url("@/assets/images/activity/cp-paradise/rewards-btn.png");
  background-size: 100% 100%;
}
</style>
