<template>
  <Teleport to="body">
    <van-overlay
      :show="showPopup"
      :lock-scroll="false"
      z-index="10002"
      @click="showPopup = false"
    >
      <div class="rewards-dialog" @click.stop="">
        <div class="box">
          <div class="title">
            <TitleBlockPopup title="Winning record"></TitleBlockPopup>
          </div>

          <div class="rewards">
            <div v-for="reward in 10">
              <div class="reward-item">
                <PrizeBox :rewards="{}"></PrizeBox>
              </div>
              <div class="reward-text">2025/06/08 22:34:51</div>
            </div>
          </div>
          <div class="confirm-btn">OK</div>
        </div>
      </div>
    </van-overlay>
  </Teleport>
</template>
<script setup>
import { computed, ref } from "vue";
import TitleBlockPopup from "./TitleBlockPopup.vue";
import PrizeBox from "./PrizeBox.vue";

const showPopup = ref(false);
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.rewards-dialog {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .box {
    width: px2rem(328);
    height: px2rem(238);
    display: flex;
    flex-direction: column;
    align-items: center;
    background-image: url("@/assets/images/activity/cp-paradise/dialog-bg.png");
    background-size: 100% 100%;
    z-index: 9999;
    position: relative;

    .title {
      margin-top: px2rem(-40);
    }
    .rewards {
      display: flex;
      overflow-x: auto;
      margin-top: px2rem(50);
      width: px2rem(286);

      .reward-item {
        width: px2rem(72);
        height: px2rem(72);
        margin-right: px2rem(11);
      }
      .reward-text {
        width: px2rem(73);
        height: px2rem(29);
        flex-shrink: 0;
        color: #c2adff;
        text-align: center;
        font-family: Gilroy;
        font-size: px2rem(12);
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        margin-top: px2rem(7);
      }
    }

    .confirm-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: px2rem(143);
      height: px2rem(42);
      position: absolute;
      bottom: px2rem(24);
      left: 50%;
      transform: translateX(-50%);
      flex-shrink: 0;
      background-image: url("@/assets/images/activity/cp-paradise/dialog-btn-bg.png");
      background-size: 100% 100%;

      color: #fff;
      text-align: center;
      font-family: Gilroy-Black;
      font-size: 16.923px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
}
</style>
