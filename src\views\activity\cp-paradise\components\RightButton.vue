<template>
  <div
    class="right-button"
    v-track:click
    trace-key="active_button_click"
    :track-params="
      JSON.stringify({
        active_button_click_name: '1',
        active_name: 'Stellar Voyage-房主开播激励活动',
      })
    "
  >
    <div class="right-button-text" @click="toRulePage()">
      {{ $t(`singer.rules_title`) }}
    </div>
  </div>
</template>
<script setup>
import { useRouter } from "vue-router";
const router = useRouter();

const toRulePage = () => {
  router.push({
    name: "StellarVoyageRule",
  });
};
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.right-button {
  height: px2rem(30);
  width: fit-content;
  position: fixed;
  right: px2rem(-2);
  top: px2rem(150);
  z-index: 999;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;

  &::after {
    content: "";
    width: px2rem(50);
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background: url("@/assets/images/activity/cp-paradise/rule-btn.png");
    background-size: 100% 100%;
  }

  .right-button-text {
    position: relative;
    z-index: 1;
    padding: 0 px2rem(4) 0 px2rem(12);
    color: #fff;
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(12);
    font-style: normal;
    font-weight: 700;
    line-height: 124%; /* 14.88px */
  }
}

.rtl-html {
  .right-button {
    right: unset;
    left: px2rem(-2);
    &::after {
      transform: scaleX(-1);
    }
    .right-button-text {
      padding: 0 px2rem(12) 0 px2rem(4);
    }
  }
}
</style>
