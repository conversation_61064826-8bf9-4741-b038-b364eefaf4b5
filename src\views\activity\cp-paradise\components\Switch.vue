<template>
  <div class="switch-wrapper">
    <div class="switch" :class="{ active: isOn }" @click="toggle">
      <div class="switch-text on" :class="{ active: isOn }">ON</div>
      <div class="switch-text off" :class="{ active: !isOn }">OFF</div>
      <div class="switch-block" :class="{ active: isOn }"></div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
const emit = defineEmits(["update:modelValue"]);
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
});

const isOn = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit("update:modelValue", value);
  },
});

const toggle = () => {
  isOn.value = !isOn.value;
};

defineExpose({
  toggle,
});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.switch-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.switch {
  position: relative;
  width: px2rem(42);
  height: px2rem(21);
  background: #906fe5;
  border-radius: px2rem(19);
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  border: px2rem(1) solid rgba(174, 146, 255, 0.5);

  &.active {
    background: #8e62ff;
    border: px2rem(1) solid #885aff;
  }
}

.switch-text {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.3s ease;
  z-index: 2;

  color: #fff;
  text-align: center;
  font-family: Gilroy;
  font-size: px2rem(10);
  font-style: normal;
  font-weight: 200;
  line-height: 1; /* 15.4px */
  text-transform: uppercase;

  &.on {
    left: px2rem(5);
    transform: translateY(-50%) scale(0.8);
    opacity: 0;

    &.active {
      transform: translateY(-50%) scale(1);
      opacity: 1;
    }
  }

  &.off {
    right: px2rem(4);
    transform: translateY(-50%) scale(0.8);
    opacity: 0;

    &.active {
      transform: translateY(-50%) scale(1);
      opacity: 1;
    }
  }
}

.switch-block {
  position: absolute;
  top: px2rem(2);
  left: px2rem(2);
  width: px2rem(15);
  height: px2rem(15);
  background: white;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 3;
  border: px2rem(2) solid #b2a2ee;
  flex-shrink: 0;

  &.active {
    transform: translateX(px2rem(21));
  }
}
</style>
