<template>
  <div class="task-button-wrapper">
    <div class="task-button"></div>
    <TasksPopup></TasksPopup>
  </div>
</template>
<script setup>
import TasksPopup from "./TasksPopup.vue";
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.task-button {
  position: fixed;
  left: px2rem(8);
  top: px2rem(150);
  width: px2rem(58);
  height: px2rem(62);
  background: url("@/assets/images/activity/cp-paradise/task-btn.png");
  background-size: 100% 100%;
  z-index: 8;
}
</style>
