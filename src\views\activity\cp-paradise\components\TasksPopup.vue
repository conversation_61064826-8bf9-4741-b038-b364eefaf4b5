<template>
  <van-overlay
    :show="showPopup"
    :lock-scroll="false"
    z-index="10002"
    @click="showPopup = false"
  >
    <div class="base-modal-wrapper" @click.stop="">
      <div class="title">
        <TitleBlockPopup title="Intimacy tasks"></TitleBlockPopup>
      </div>
      <div class="desc">
        Complete the following tasks to gain intimacy points or rewards
      </div>
      <div class="task-list">
        <div class="task-item" v-for="task in taskList">
          <gap :gap="7"></gap>
          <div
            class="cover"
            :style="{
              backgroundImage: `url(${task.icon})`,
            }"
          ></div>
          <gap :gap="6"></gap>
          <div class="content">
            <div class="task-name">{{ task.name }}</div>
            <div class="rewards">
              <div
                class="reward-item"
                v-if="task.type === 1"
                v-for="reward in task.rewards"
              >
                <PrizeBox :rewards="reward"></PrizeBox>
              </div>
            </div>
            <div class="value-box" v-if="task.type !== 1 && task.type !== 6">
              <gender-coin :size="16"></gender-coin>
              <div>x</div>
              <div>1</div>
              <div>=</div>
              <div class="icon-heart"></div>
              <div>x</div>
              <div>1</div>
            </div>
            <div class="value-box" v-if="task.type === 2">
              <div>1 round of dialogue</div>
              <div>=</div>
              <div class="icon-heart"></div>
              <div>x</div>
              <div>1</div>
            </div>
            <div class="value-box" v-if="task.type === 6">
              <div>like</div>
              <div>x</div>
              <div>1</div>
              <div>=</div>
              <div class="icon-heart"></div>
              <div>x</div>
              <div>1</div>
            </div>
          </div>
          <gap :gap="6"></gap>
          <div class="action claim">Go</div>
          <gap :gap="10"></gap>
        </div>
      </div>
    </div>
  </van-overlay>
</template>
<script setup>
import { computed, ref } from "vue";
import TitleBlockPopup from "./TitleBlockPopup.vue";
import PrizeBox from "./PrizeBox.vue";
import TaskIcon1 from "@/assets/images/activity/cp-paradise/task-1.png";
import TaskIcon2 from "@/assets/images/activity/cp-paradise/task-2.png";
import TaskIcon3 from "@/assets/images/activity/cp-paradise/task-3.png";
import TaskIcon4 from "@/assets/images/activity/cp-paradise/task-4.png";
import TaskIcon5 from "@/assets/images/activity/cp-paradise/task-5.png";
import TaskIcon6 from "@/assets/images/activity/cp-paradise/task-6.png";

const showPopup = ref(false);

const taskList = computed(() => [
  {
    name: "Sign in",
    type: 1,
    status: 1,
    icon: TaskIcon1,
    rewards: [
      {
        reward_img: "",
        reward_type: 1,
        num: 100,
      },
    ],
  },
  {
    name: "Message chat with your partner",
    type: 2,
    status: 1,
    icon: TaskIcon2,
  },
  {
    name: "Send activity gifts to your partner",
    type: 3,
    status: 1,
    icon: TaskIcon3,
  },
  {
    name: "Voice call",
    type: 4,
    status: 1,
    icon: TaskIcon4,
  },
  {
    name: "Video call",
    type: 5,
    status: 1,
    icon: TaskIcon5,
  },
  {
    name: "Like your partner's moment",
    type: 6,
    status: 1,
    icon: TaskIcon6,
  },
]);
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.base-modal-wrapper {
  width: 100%;
  height: px2rem(492);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: fixed;
  bottom: 0;
  background-image: url("@/assets/images/activity/cp-paradise/popup-bg.png");
  background-size: 100% 100%;

  .title {
    margin-top: px2rem(-5);
  }

  .desc {
    color: #ffe7fd;
    text-align: center;
    font-family: Gilroy-Regular;
    font-size: px2rem(12);
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-top: px2rem(27);
    width: px2rem(297);
  }

  .task-list {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: px2rem(12);
    flex-grow: 1;
    overflow-y: auto;
    .task-item {
      width: px2rem(348);
      padding: px2rem(10) 0;
      flex-shrink: 0;
      border-radius: px2rem(12);
      border: px2rem(1) solid #fff;
      background: #7a4ffc;
      margin-bottom: px2rem(10);

      display: flex;
      align-items: center;

      .cover {
        width: px2rem(68);
        height: px2rem(68);
        background-size: 100% 100%;
        flex-shrink: 0;
      }
      .content {
        flex-grow: 1;

        .task-name {
          color: #f5e8ff;
          font-family: Gilroy-Medium;
          font-size: px2rem(14);
          font-style: normal;
          font-weight: 400;
          line-height: 120%; /* 16.8px */
        }

        .rewards {
          display: flex;
          overflow-x: auto;
          margin-top: px2rem(6);
          width: px2rem(168);

          .reward-item {
            width: px2rem(38);
            height: px2rem(38);
            margin-right: px2rem(7);
          }
        }

        .value-box {
          display: flex;
          align-items: center;

          color: #c395ff;
          font-family: Gilroy-Medium;
          font-size: px2rem(11);
          font-style: normal;
          font-weight: 400;
          line-height: normal;

          .icon-heart {
            width: px2rem(20);
            height: px2rem(20);
            background-size: 100% 100%;
            background-image: url("@/assets/images/activity/cp-paradise/heart.png");
          }
        }
      }
      .action {
        width: px2rem(62);
        height: px2rem(25);
        flex-shrink: 0;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        color: #fff;
        text-align: center;
        font-family: Gilroy;
        font-size: px2rem(10);
        font-style: normal;
        font-weight: 700;
        line-height: normal;
      }

      .go {
        background-image: url("@/assets/images/activity/cp-paradise/go-btn.png");
      }
      .claim {
        background-image: url("@/assets/images/activity/cp-paradise/claim-btn.png");
      }
      .claimed {
        background-image: url("@/assets/images/activity/cp-paradise/claimed-btn.png");
      }
    }
  }
}
</style>
