<template>
  <div :class="showBg ? 'title-block-out' : ''">
    <div
      class="title-block"
      :data-title="title"
      :style="{
        fontSize: `${$pxToRemPx(fontSize)}px`,
        lineHeight: `${$pxToRemPx(lineHeight)}px`,
        minWidth: `${$pxToRemPx(minWidth)}px`,
      }"
    >
      <div>{{ title }}</div>
    </div>
  </div>
</template>
<script setup>
defineProps({
  title: {
    type: String,
  },
  showBg: {
    type: Boolean,
    default: true,
  },
  fontSize: {
    type: Number,
    default: 18,
  },
  lineHeight: {
    type: Number,
    default: 35,
  },
  minWidth: {
    type: Number,
    default: 220,
  },
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.title-block-out {
  background: linear-gradient(
    90deg,
    rgba(137, 98, 255, 0) 0%,
    #8367ff 47.77%,
    rgba(137, 98, 255, 0) 100%
  );
  &::before,
  &::after {
    content: "";
    display: block;
    height: px2rem(1);
    width: 100%;
    background: linear-gradient(
      90deg,
      rgba(49, 69, 203, 0) 0%,
      #dabcff 51.88%,
      rgba(49, 69, 203, 0) 100%
    );
  }
}
.title-block {
  position: relative;
  margin: 0 auto;
  padding: px2rem(4) 0;
  div {
    text-align: center;
    color: #fff;
    text-shadow: px2rem(0.5) 0 #8962ff;
    font-family: "DIN Next W1G";
    font-size: px2rem(14);
    font-style: normal;
    font-weight: 500;
    line-height: px2rem(14);
  }
}
</style>
