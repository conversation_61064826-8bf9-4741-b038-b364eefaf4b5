<template>
  <div class="title-block-box">
    <gap :gap="8"></gap>
    <div
      class="title-block"
      :data-title="title"
      :style="{
        fontSize: `${$pxToRemPx(fontSize)}px`,
        lineHeight: `${$pxToRemPx(lineHeight)}px`,
      }"
    >
      <div>{{ title }}</div>
    </div>
    <gap :gap="8"></gap>
  </div>
</template>
<script setup>
defineProps({
  title: {
    type: String,
  },
  fontSize: {
    type: Number,
    default: 18,
  },
  lineHeight: {
    type: Number,
    default: 35,
  },
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.title-block-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  &::before,
  &::after {
    content: "";
    width: px2rem(45);
    height: px2rem(17);
    flex-shrink: 0;
    background: url("@/assets/images/activity/cp-paradise/title-right.png");
    background-size: 100% 100%;
  }
  &::before {
    transform: scaleX(-1);
  }
}

.title-block {
  text-align: center;
  width: fit-content;
  font-family: <PERSON>roy;
  font-style: normal;
  font-weight: 900;
  position: relative;
  flex-shrink: 0;
  z-index: 0;
  div {
    text-shadow: 0 0 px2rem(12) rgba(192, 133, 255, 0.2);
    -webkit-text-stroke-width: px2rem(2);
    -webkit-text-stroke-color: #9b7aff;
    position: absolute;
    inset: 0;
    z-index: 0;
  }
  &::after {
    content: attr(data-title);
    position: relative;
    z-index: 1;
    background: linear-gradient(180deg, #fff 39.22%, #f7e0ff 77.26%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
</style>
