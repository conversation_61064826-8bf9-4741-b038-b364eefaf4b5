<template>
  <div class="title-block-box">
    <div
      class="title-block"
      :data-title="title"
      :style="{
        fontSize: `${$pxToRemPx(fontSize)}px`,
        lineHeight: `${$pxToRemPx(lineHeight)}px`,
      }"
    >
      <div>{{ title }}</div>
    </div>
  </div>
</template>
<script setup>
defineProps({
  title: {
    type: String,
  },
  fontSize: {
    type: Number,
    default: 33,
  },
  lineHeight: {
    type: Number,
    default: 35,
  },
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.title-block-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.title-block {
  text-align: center;
  width: fit-content;
  font-family: Gilroy;
  font-style: italic;
  font-weight: 900;
  position: relative;
  flex-shrink: 0;
  z-index: 0;
  div {
    text-shadow: 0 0 px2rem(12) rgba(192, 133, 255, 0.2);
    -webkit-text-stroke-width: px2rem(2);
    -webkit-text-stroke-color: #9b7aff;
    position: absolute;
    inset: 0;
    z-index: 0;
  }
  &::after {
    content: attr(data-title);
    position: relative;
    z-index: 1;
    padding: 0 15px;
    background: linear-gradient(
      183deg,
      #fff 46.37%,
      #c5c7ff 54.88%,
      #f7f3ff 63.6%,
      #f5c4ff 70.73%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
</style>
