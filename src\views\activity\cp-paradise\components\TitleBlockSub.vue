<template>
  <div :class="showBg ? 'title-block-out' : ''">
    <div
      class="title-block"
      :data-title="title"
      :style="{
        fontSize: `${$pxToRemPx(fontSize)}px`,
        lineHeight: `${$pxToRemPx(lineHeight)}px`,
      }"
    >
      <div>{{ title }}</div>
    </div>
  </div>
</template>
<script setup>
defineProps({
  title: {
    type: String,
  },
  showBg: {
    type: Boolean,
    default: true,
  },
  fontSize: {
    type: Number,
    default: 15,
  },
  lineHeight: {
    type: Number,
    default: 28,
  },
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.title-block-out {
  width: fit-content;
  background: linear-gradient(
    90deg,
    rgba(137, 98, 255, 0) 0%,
    #fe73ff 47.77%,
    rgba(137, 98, 255, 0) 100%
  );
  &::before,
  &::after {
    content: "";
    display: block;
    height: px2rem(1);
    width: 100%;
    background: linear-gradient(
      90deg,
      rgba(49, 69, 203, 0) 0%,
      #dabcff 51.88%,
      rgba(49, 69, 203, 0) 100%
    );
  }
}
.title-block {
  position: relative;
  width: fit-content;
  margin: 0 auto;
  padding: px2rem(4) px2rem(8);
  display: flex;
  align-items: center;
  justify-content: center;
  height: px2rem(28);
  &::before,
  &::after {
    content: "";
    width: px2rem(26);
    height: px2rem(6);
    flex-shrink: 0;
    background: url("@/assets/images/activity/cp-paradise/sub-title-right.png");
    background-size: 100% 100%;
  }
  &::before {
    transform: scaleX(-1);
  }
  div {
    width: fit-content;
    text-align: center;
    color: #fff;
    text-shadow: px2rem(0.5) 0 #fe73ff;
    font-family: "DIN Next W1G";
    font-size: px2rem(14);
    font-style: normal;
    font-weight: 700;
    line-height: px2rem(14);
  }
}
</style>
