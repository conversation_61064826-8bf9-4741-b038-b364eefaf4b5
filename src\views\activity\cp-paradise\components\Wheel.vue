<template>
  <div class="wheel-wrapper" data-theme="advanced">
    <div class="animation-switch">
      <div class="switch-text">skip animation</div>
      <gap :gap="5"></gap>
      <div>
        <Switch v-model="isAnimation"></Switch>
      </div>
    </div>
    <div class="wheel">
      <div class="wheel-bg" :style="{ transform: `rotate(${angle}deg)` }">
        <div class="prizes">
          <div
            class="prize"
            v-for="(,index) in 8"
            :style="{ transform: `rotate(${index * 45}deg)` }"
          >
            <div
              class="prize-inner"
              :style="{ transform: `rotate(${index * 45 * -1 - angle}deg)` }"
            ></div>
          </div>
        </div>
      </div>
      <div class="center">
        <div class="text">Points Pool</div>
        <div class="points">
          <div
            class="point"
            :class="index === 1 ? `point-active` : ''"
            v-for="(,index) in 8"
            :key="index"
            :style="{ transform: `rotate(${index * 45}deg)` }"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import Switch from "./Switch.vue";
import { ref } from "vue";

const isAnimation = ref(true);
const angle = ref(15);



</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
[data-theme="normal"] {
  --wheel-bg: url("@/assets/images/activity/cp-paradise/wheel/normal/wheel-bg.png");
  --wheel-center-bg: url("@/assets/images/activity/cp-paradise/wheel/normal/center.png");
  --point-bg: url("@/assets/images/activity/cp-paradise/wheel/normal/point.png");

  --prize-normal-bg: url("@/assets/images/activity/cp-paradise/wheel/normal/prize-normal.png");
  --prize-active-bg: url("@/assets/images/activity/cp-paradise/wheel/normal/prize-active.png");
  --prize-target-bg: url("@/assets/images/activity/cp-paradise/wheel/normal/prize-target.png");
}

[data-theme="advanced"] {
  --wheel-bg: url("@/assets/images/activity/cp-paradise/wheel/advanced/wheel-bg.png");
  --wheel-center-bg: url("@/assets/images/activity/cp-paradise/wheel/advanced/center.png");
  --point-bg: url("@/assets/images/activity/cp-paradise/wheel/advanced/point.png");

  --prize-normal-bg: url("@/assets/images/activity/cp-paradise/wheel/advanced/prize-normal.png");
  --prize-active-bg: url("@/assets/images/activity/cp-paradise/wheel/advanced/prize-active.png");
  --prize-target-bg: url("@/assets/images/activity/cp-paradise/wheel/advanced/prize-target.png");
}

.wheel-wrapper {
  width: px2rem(390);
  height: px2rem(416);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .animation-switch {
    position: absolute;
    top: 0;
    right: px2rem(17);
    display: flex;
    align-items: center;

    .switch-text {
      color: #fff;
      text-align: center;
      font-family: Gilroy-SemiBold;
      font-size: px2rem(10);
      font-style: normal;
      font-weight: 400;
      line-height: 140%; /* 14px */
      text-transform: capitalize;
    }
  }

  .wheel {
    width: px2rem(305);
    height: px2rem(305);
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .wheel-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: var(--wheel-bg);
      background-size: 100% 100%;
    }

    .center {
      width: px2rem(160);
      height: px2rem(160);
      flex-shrink: 0;
      background-image: var(--wheel-center-bg);
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 1;

      .text {
        width: px2rem(50);
        color: #fff;
        text-align: center;
        text-shadow: 0 0 px2rem(2) rgba(255, 255, 255, 0.3),
          0 0 px2rem(10) rgba(255, 59, 199, 0.79);
        font-family: Gilroy;
        font-size: px2rem(16);
        font-style: normal;
        font-weight: 400;
        line-height: 100%; /* 16px */
      }

      .points {
        position: absolute;
        inset: 0;
        .point {
          position: absolute;
          width: px2rem(50);
          height: px2rem(50);
          background-image: var(--point-bg);
          background-size: 100% 100%;
          top: px2rem(6);
          left: 50%;
          transform-origin: 50% px2rem(75);
          margin-left: px2rem(-25);
        }

        .point-active {
          background-image: url("@/assets/images/activity/cp-paradise/wheel/advanced/point-light.png");
        }
      }
    }

    .prizes {
      position: absolute;
      inset: 0;
      .prize {
        position: absolute;
        top: px2rem(-56);
        left: 50%;
        margin-left: px2rem(-68);
        width: px2rem(136);
        height: px2rem(136);
        flex-shrink: 0;
        transform-origin: 50% px2rem(208);

        .prize-inner {
          width: 100%;
          height: 100%;
          background-size: 100% 100%;
          background-image: var(--prize-normal-bg);
        }
      }
    }
  }
}
</style>
