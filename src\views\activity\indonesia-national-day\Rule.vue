<template>
  <div class="indonesia-national-day-rule">
    <header-bar
      back-icon-show
      :show-title="true"
      :title="$t('indonesia.greeting')"
      close-type="back"
      is-scroll-change-bg
      :padding="false"
      :contentPadding="false"
      :always-show-right-icon="true"
      @emit:scrollChangeBgShow="scrollChangeBgShow"
    >
      <template #right>
        <svg-icon
          class="icon-concat"
          :icon="showPageTitle ? 'contact-black' : 'contact-white'"
          :color="showPageTitle ? '#000' : '#fff'"
          @click="goFeedback()"
          v-track:click
          trace-key="active_button_click"
          :track-params="JSON.stringify({ active_button_click_name: '2' })"
        />
      </template>
    </header-bar>
    <div class="bg-box">
      <div
        class="banner-title"
        :style="{
          backgroundImage: `url('${getImageUrl('title.png')}')`,
        }"
      >
        <img :src="getImageUrl('title.png')" draggable="false" />
      </div>
    </div>
    <div class="tabs">
      <div
        class="tab"
        :class="current === 0 ? 'active' : ''"
        @click="current = 0"
      >
        <span>{{ $t("indonesia.rewards") }}</span>
      </div>
      <gap :gap="6"></gap>
      <div
        class="tab"
        :class="current === 1 ? 'active' : ''"
        @click="current = 1"
      >
        <span>{{ $t("indonesia.rules") }}</span>
      </div>
    </div>
    <div v-if="current === 0" class="rewards-list">
      <div class="sub-tabs">
        <div
          class="sub-tab"
          :class="rewardTab === 0 ? 'active' : ''"
          @click="rewardTab = 0"
        >
          {{ $t("indonesia.task_rewards") }}
        </div>
        <gap :gap="4"></gap>
        <div
          class="sub-tab"
          :class="rewardTab === 1 ? 'active' : ''"
          @click="rewardTab = 1"
        >
          {{ $t("indonesia.gift_receiving_ranking_rewards") }}
        </div>
        <gap :gap="4"></gap>
        <div
          class="sub-tab"
          :class="rewardTab === 2 ? 'active' : ''"
          @click="rewardTab = 2"
        >
          {{ $t("indonesia.gift_giving_ranking_rewards") }}
        </div>
        <gap :gap="4"></gap>
        <div
          class="sub-tab"
          :class="rewardTab === 3 ? 'active' : ''"
          @click="rewardTab = 3"
        >
          {{ $t("indonesia.room_gift_ranking_rewards") }}
        </div>
      </div>
      <template v-if="rewardTab === 0">
        <div class="box-1">
          <GoldBox>
            <div class="rewards">
              <div class="reward" v-for="reward in taskRewards">
                <div class="cover">
                  <PrizeBox :rewards="reward"></PrizeBox>
                </div>
                <div class="name">{{ reward.reward_name }}</div>
              </div>
            </div>
          </GoldBox>
        </div>
      </template>
      <template v-if="rewardTab === 1">
        <div class="box-2">
          <GoldBox>
            <div class="title">
              <BlockTitle>
                <span class="top1-title">
                  {{ $t("indonesia.top1") }}
                </span>
              </BlockTitle>
            </div>
            <div class="rewards">
              <div
                class="reward"
                v-for="reward in rewardsInfo?.receive?.top1 || []"
              >
                <div class="cover">
                  <PrizeBox :rewards="reward"></PrizeBox>
                </div>
                <div class="name">{{ reward.reward_name }}</div>
              </div>
            </div>
          </GoldBox>
        </div>
        <div class="box-3">
          <GoldBox>
            <div class="title">
              <BlockTitle>
                <span class="top2-title">
                  {{ $t("indonesia.top2") }}
                </span>
              </BlockTitle>
            </div>
            <div class="rewards">
              <div
                class="reward"
                v-for="reward in rewardsInfo?.receive?.top2 || []"
              >
                <div class="cover">
                  <PrizeBox :rewards="reward"></PrizeBox>
                </div>
                <div class="name">{{ reward.reward_name }}</div>
              </div>
            </div>
          </GoldBox>
        </div>
        <div class="box-4">
          <GoldBox>
            <div class="title">
              <BlockTitle>
                <span class="top3-title">
                  {{ $t("indonesia.top3") }}
                </span>
              </BlockTitle>
            </div>
            <div class="rewards">
              <div
                class="reward"
                v-for="reward in rewardsInfo?.receive?.top3 || []"
              >
                <div class="cover">
                  <PrizeBox :rewards="reward"></PrizeBox>
                </div>
                <div class="name">{{ reward.reward_name }}</div>
              </div>
            </div>
          </GoldBox>
        </div>
      </template>
      <template v-if="rewardTab === 2">
        <div class="box-2">
          <GoldBox>
            <div class="title">
              <BlockTitle>
                <span class="top1-title">{{ $t("indonesia.top1") }}</span>
              </BlockTitle>
            </div>
            <div class="rewards">
              <div
                class="reward"
                v-for="reward in rewardsInfo?.send?.top1 || []"
              >
                <div class="cover">
                  <PrizeBox :rewards="reward"></PrizeBox>
                </div>
                <div class="name">{{ reward.reward_name }}</div>
              </div>
            </div>
          </GoldBox>
        </div>
        <div class="box-3">
          <GoldBox>
            <div class="title">
              <BlockTitle>
                <span class="top2-title">{{ $t("indonesia.top2") }}</span>
              </BlockTitle>
            </div>
            <div class="rewards">
              <div
                class="reward"
                v-for="reward in rewardsInfo?.send?.top2 || []"
              >
                <div class="cover">
                  <PrizeBox :rewards="reward"></PrizeBox>
                </div>
                <div class="name">{{ reward.reward_name }}</div>
              </div>
            </div>
          </GoldBox>
        </div>
        <div class="box-4">
          <GoldBox>
            <div class="title">
              <BlockTitle>
                <span class="top3-title">{{ $t("indonesia.top3") }}</span>
              </BlockTitle>
            </div>
            <div class="rewards">
              <div
                class="reward"
                v-for="reward in rewardsInfo?.send?.top3 || []"
              >
                <div class="cover">
                  <PrizeBox :rewards="reward"></PrizeBox>
                </div>
                <div class="name">{{ reward.reward_name }}</div>
              </div>
            </div>
          </GoldBox>
        </div>
      </template>
      <template v-if="rewardTab === 3">
        <div class="box-2">
          <GoldBox>
            <div class="title">
              <BlockTitle>
                <span class="top1-title">{{
                  $t("indonesia.top1_room_owner")
                }}</span>
              </BlockTitle>
            </div>
            <div class="rewards">
              <div
                class="reward"
                v-for="reward in rewardsInfo?.room_receive?.top1 || []"
              >
                <div class="cover">
                  <PrizeBox :rewards="reward"></PrizeBox>
                </div>
                <div class="name">{{ reward.reward_name }}</div>
              </div>
            </div>
          </GoldBox>
        </div>
        <div class="box-3">
          <GoldBox>
            <div class="title">
              <BlockTitle>
                <span class="top2-title">{{
                  $t("indonesia.top2_room_owner")
                }}</span>
              </BlockTitle>
            </div>
            <div class="rewards">
              <div
                class="reward"
                v-for="reward in rewardsInfo?.room_receive?.top2 || []"
              >
                <div class="cover">
                  <PrizeBox :rewards="reward"></PrizeBox>
                </div>
                <div class="name">{{ reward.reward_name }}</div>
              </div>
            </div>
          </GoldBox>
        </div>
        <div class="box-4">
          <GoldBox>
            <div class="title">
              <BlockTitle>
                <span class="top3-title">{{
                  $t("indonesia.top3_room_owner")
                }}</span>
              </BlockTitle>
            </div>
            <div class="rewards">
              <div
                class="reward"
                v-for="reward in rewardsInfo?.room_receive?.top3 || []"
              >
                <div class="cover">
                  <PrizeBox :rewards="reward"></PrizeBox>
                </div>
                <div class="name">{{ reward.reward_name }}</div>
              </div>
            </div>
          </GoldBox>
        </div>
      </template>
    </div>
    <div v-if="current === 1" class="rules">
      <div class="rule-item">
        <div class="rule-row">
          <div class="rule-label">
            {{ $t("indonesia.rules_activity_time_label") }}
          </div>
          <div class="rule-content">
            {{ $t("indonesia.rules_activity_time") }}
          </div>
        </div>
      </div>
      <div class="rule-item">
        <div class="rule-row">
          <div class="rule-label">
            {{ $t("indonesia.rules_activity_gifts_label") }}
          </div>
          <div class="rule-content">
            {{ $t("indonesia.rules_activity_gifts") }}
          </div>
        </div>
      </div>

      <div class="rewards">
        <div class="reward" v-for="reward in activityInfo?.gifts || []">
          <div class="cover">
            <PrizeBox :rewards="reward" :showTag="false"></PrizeBox>
          </div>
          <div class="coin-box">
            <GenderCoin :size="18"></GenderCoin>
            <gap :gap="3"></gap>
            <div class="coin-number">
              {{ formatCount(reward?.coin ?? 0) }}
            </div>
          </div>
        </div>
      </div>

      <div class="rule-item">
        <div class="rule-row">
          <div class="rule-label">{{ $t("indonesia.rules_tasks_label") }}</div>
          <div class="rule-content">
            {{ $t("indonesia.rules_tasks") }}
          </div>
        </div>
        <div class="rule-row">
          <div class="rule-label">
            {{ $t("indonesia.rules_daily_tasks_label") }}
          </div>
          <div class="rule-content">
            {{ $t("indonesia.rules_daily_tasks") }}
          </div>
        </div>
        <div class="rule-row">
          <div class="rule-label">
            {{ $t("indonesia.rules_limited_tasks_label") }}
          </div>
          <div class="rule-content">
            {{ $t("indonesia.rules_limited_tasks") }}
          </div>
        </div>
      </div>

      <div class="rule-item">
        <div class="rule-row">
          <div class="rule-label">
            {{ $t("indonesia.rules_rankings_label") }}
          </div>
          <div class="rule-content">
            {{ $t("indonesia.rules_rankings") }}
          </div>
        </div>
        <div class="rule-row">
          <div class="rule-label">
            {{ $t("indonesia.rules_gift_receiving_ranking_label") }}
          </div>
          <div class="rule-content">
            {{ $t("indonesia.rules_gift_receiving_ranking") }}
          </div>
        </div>
        <div class="rule-row">
          <div class="rule-label">
            {{ $t("indonesia.rules_gift_giving_ranking_label") }}
          </div>
          <div class="rule-content">
            {{ $t("indonesia.rules_gift_giving_ranking") }}
          </div>
        </div>
        <div class="rule-row">
          <div class="rule-label">
            {{ $t("indonesia.rules_room_gift_ranking_label") }}
          </div>
          <div class="rule-content">
            {{ $t("indonesia.rules_room_gift_ranking") }}
          </div>
        </div>
      </div>

      <div class="rule-item">
        <div class="rule-row">
          <div class="rule-content">
            {{ $t("indonesia.rules_feedback") }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed, ref } from "vue";
import { getLang } from "@/i18n/index.js";
import GoldBox from "./components/GoldBox.vue";
import BlockTitle from "./components/BlockTitle.vue";
import PrizeBox from "./components/PrizeBox.vue";
import GenderCoin from "@/components/gender-coin/index.vue";
import { useRouter } from "vue-router";
import activityApi from "@/api/activity.js";
import { formatCount } from "@/utils/util.js";

const router = useRouter();
const current = ref(0);
const rewardTab = ref(0);
const lang = getLang();
const activityInfo = ref();
const rewardsInfo = ref();

const taskRewards = computed(() => {
  return [
    ...(rewardsInfo.value?.task1 || []),
    ...(rewardsInfo.value?.task2 || []),
    ...(rewardsInfo.value?.task3 || []),
    ...(rewardsInfo.value?.task4 || []),
    ...(rewardsInfo.value?.task5 || []),
  ];
});

function getImageUrl(name) {
  return new URL(
    `../../../assets/images/activity/indonesia-national-day/${lang}/${name}`,
    import.meta.url
  ).href;
}

const fetchData = async () => {
  const res = await activityApi.egypt_info();
  if (res.code === 200) {
    activityInfo.value = res.data;
  }
  const res2 = await activityApi.egypt_rewards();
  if (res2.code === 200) {
    rewardsInfo.value = res2.data;
  }
};

// 下滑展示标题处理
const showPageTitle = ref(false);
const scrollChangeBgShow = (show) => {
  showPageTitle.value = show;
};

const goFeedback = () => {
  router.push({
    name: "Feedback",
    query: {
      type: 15,
    },
  });
};

fetchData();
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.indonesia-national-day-rule {
  min-height: 100vh;
  width: 100%;
  background-color: #5b1303;
  overflow: hidden;
  background-image: url("@/assets/images/activity/indonesia-national-day/bg-1.jpg");
  background-size: 100% px2rem(476);
  background-repeat: no-repeat;
  .icon-concat {
    font-size: px2rem(28);
    color: unset;
  }
  .bg-box {
    width: 100%;
    overflow: hidden;
    .banner-title {
      width: px2rem(223);
      background-size: 100% 100%;
      margin: 0 auto;
      margin-top: px2rem(149);
      img {
        opacity: 0;
        width: px2rem(223);
        vertical-align: bottom;
      }
    }
    .count-down {
      margin-top: px2rem(122);
    }
  }
  .tabs {
    display: flex;
    width: px2rem(342);
    height: px2rem(52);
    display: flex;
    padding: px2rem(4);
    align-items: center;
    border-radius: px2rem(31);
    border: 1px solid #ffdb8d;
    background: #cc3a34;
    margin: 0 auto;
    margin-top: px2rem(251);

    .tab {
      width: px2rem(164);
      height: px2rem(44);
      flex-shrink: 0;
      border-radius: px2rem(50);
      border: px2rem(2) solid #ff8781;
      background: linear-gradient(180deg, #8f1914 0%, #250200 100%);

      color: #ffa19d;
      text-align: center;
      text-shadow: 0px px2rem(4) px2rem(4) rgba(0, 0, 0, 0.25);
      font-family: Gilroy;
      font-size: px2rem(20);
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .tab.active {
      border: px2rem(1.5) solid #fff;
      background: linear-gradient(
        180deg,
        #ffad9e 0%,
        #d90000 47.12%,
        #8d0000 85.58%
      );
      box-shadow: 0px -4px 4px 0px rgba(44, 2, 104, 0.3) inset,
        0px 3px 3px 0px rgba(255, 255, 255, 0.66) inset;
      span {
        text-align: center;
        text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
        font-family: Gilroy;
        font-size: px2rem(20);
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        background: linear-gradient(180deg, #fff 28.26%, #ffae00 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
  .rewards-list {
    padding-bottom: px2rem(44);
  }
  .sub-tabs {
    width: px2rem(356);
    display: flex;
    margin: 0 auto;
    margin-top: px2rem(24);
    .sub-tab {
      width: px2rem(86);
      height: px2rem(40);
      padding: px2rem(7);
      flex-shrink: 0;
      border-radius: px2rem(8);
      border: px2rem(1) solid #cf817e;
      background: linear-gradient(180deg, #881612 0%, #320503 100%);

      color: #ffa19d;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(9);
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .sub-tab.active {
      color: #ffefbc;
      border: px2rem(1) solid #ffdb8d;

      background: linear-gradient(
        180deg,
        #ff4542 0%,
        #d70200 47.91%,
        #8b0000 100%
      );
    }
  }
  .box-1,
  .box-2,
  .box-3,
  .box-4 {
    width: px2rem(355);
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
    .title {
      margin-top: px2rem(16);
      .top1-title {
        color: #fff;
        text-align: center;
        text-shadow: 0px 0px px2rem(11) rgba(255, 232, 54, 0.74);
        -webkit-text-stroke-width: 1px;
        -webkit-text-stroke-color: #ffa600;
        font-family: Gilroy;
        font-size: px2rem(18);
        font-style: italic;
        font-weight: 900;
        line-height: normal;
        padding: 0 px2rem(3);
      }
      .top2-title {
        color: #fff;
        text-align: center;
        -webkit-text-stroke-width: 1px;
        -webkit-text-stroke-color: #296ef9;
        font-family: Gilroy;
        font-size: px2rem(18);
        font-style: italic;
        font-weight: 900;
        line-height: normal;
        padding: 0 px2rem(3);
      }
      .top3-title {
        color: #fff;
        text-align: center;
        -webkit-text-stroke-width: 1px;
        -webkit-text-stroke-color: #ff853a;
        font-family: Gilroy;
        font-size: px2rem(18);
        font-style: italic;
        font-weight: 900;
        line-height: normal;
        padding: 0 px2rem(3);

        background: linear-gradient(180deg, #fff 0%, #ff853a 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    .desc {
      margin-top: px2rem(6);
      color: #c8534e;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(14);
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      width: px2rem(217);
    }
    .rewards {
      width: 100%;
      margin: px2rem(32) 0;
      margin-bottom: 0;
      padding: 0 px2rem(18);
      flex-wrap: wrap;
      display: flex;
      align-items: flex-start;
      justify-content: center;
      flex-wrap: wrap;
      .reward {
        display: flex;
        align-items: center;
        flex-direction: column;
        width: px2rem(87);
        margin-bottom: px2rem(27);
        .cover {
          width: px2rem(70);
          height: px2rem(70);
        }
        .name {
          margin-top: px2rem(12);
          width: px2rem(87);
          color: #ffa19d;
          text-align: center;
          font-family: Gilroy;
          font-size: px2rem(14);
          font-style: normal;
          font-weight: 500;
          line-height: 100%; /* 14px */
        }
      }
    }
  }
  .box-1 {
    margin-top: px2rem(20);
  }
  .box-2,
  .box-3 {
    margin-top: px2rem(37);
  }
  .box-4 {
    margin-top: px2rem(28);
  }
  .rules {
    margin-top: px2rem(22);
    padding: 0 px2rem(36);
    padding-bottom: px2rem(68);
    .rule-item {
      border-radius: px2rem(8);
      background: rgba(255, 255, 255, 0.1);
      padding: 0 px2rem(11);
      padding-top: px2rem(13);
      padding-bottom: px2rem(5);
      margin-bottom: px2rem(22);
      .rule-row {
        margin-bottom: px2rem(10);
        .rule-label {
          border-radius: px2rem(20);
          background: rgba(223, 27, 25, 0.5);
          display: inline-flex;
          padding: px2rem(4) px2rem(8);
          justify-content: center;
          align-items: center;
          color: #ffcac8;
          font-family: Gilroy;
          font-size: px2rem(13);
          font-style: normal;
          font-weight: 500;
          line-height: normal;
          float: left;
          margin-right: px2rem(4);
          margin-top: px2rem(-3);
        }
        .rule-content {
          color: #c57e7a;
          font-family: Gilroy;
          font-size: px2rem(13);
          font-style: normal;
          font-weight: 500;
          line-height: 190%; /* 19.5px */
        }
      }
    }
  }
}
.rewards {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: px2rem(16) 0 px2rem(45) 0;
  .reward {
    display: flex;
    align-items: center;
    flex-direction: column;
    width: px2rem(87);
    .cover {
      width: px2rem(70);
      height: px2rem(70);
    }
    .name {
      margin-top: px2rem(12);
      width: px2rem(87);
      color: #ffa19d;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(14);
      font-style: normal;
      font-weight: 500;
      line-height: 100%; /* 14px */
    }
  }
}
.coin-box {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: px2rem(11);
  .coin-number {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffa19d;
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(14);
    font-style: normal;
    font-weight: 500;
    line-height: 100%; /* 14px */
  }
}

.rtl-html {
  .rule-label {
    float: right !important;
    margin-right: px2rem(0) !important;
    margin-left: px2rem(4) !important;
  }
}

.lang-zh_cn,
.lang-zh_tw {
  .top1-title {
    background: linear-gradient(180deg, #fff 0%, #ffa600 100%);
  }
  .top2-title {
    background: linear-gradient(180deg, #fff 0%, #296ef9 100%);
  }
  .top3-title {
    background: linear-gradient(180deg, #fff 0%, #c04900 100%);
  }
  .top1-title,
  .top2-title,
  .top3-title {
    -webkit-text-stroke-width: 0 !important;
    -webkit-text-stroke-color: unset !important;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
</style>
