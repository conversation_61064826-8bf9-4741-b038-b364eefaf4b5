<template>
  <div class="gb-outer-border">
    <div class="r-1"></div>
    <div class="r-2"></div>
    <div class="gb-inside-border">
      <div class="gb-inner">
        <slot></slot>
      </div>
    </div>
  </div>
</template>
<script setup></script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.gb-outer-border {
  width: 100%;
  height: fit-content;
  padding: px2rem(3);
  border-radius: px2rem(41);
  background: linear-gradient(135deg, #ffecaa 0%, #ffecaa 50%, #f36e25 100%);
  position: relative;
  .r-1,
  .r-2 {
    position: absolute;
    z-index: 2;
    left: px2rem(-6);
    top: px2rem(-5);
    height: px2rem(72);
    width: calc(100% + px2rem(12));
    &::before,
    &::after {
      content: "";
      position: absolute;
      background-image: url("@/assets/images/activity/indonesia-national-day/rad.png");
      background-size: px2rem(68) px2rem(72);
      background-repeat: no-repeat;
      background-position: top left;
      width: px2rem(68);
      height: px2rem(72);
    }
    &::after {
      right: 0;
      transform: scaleX(-1);
    }
  }
  .r-2 {
    top: unset;
    bottom: px2rem(-5);
    transform: scaleY(-1);
  }
  .gb-inside-border {
    border-radius: px2rem(40);
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #e66c23 0%, #e87f2d 50%, #ffdcad 100%);
    padding: px2rem(3);
    .gb-inner {
      position: relative;
      background: #880600;
      border-radius: px2rem(39);
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      height: 100%;
      &::after {
        content: "";
        position: absolute;
        inset: 0;
        background-image: url("@/assets/images/activity/indonesia-national-day/box-inner.png");
        background-size: px2rem(346) px2rem(300);
        border-radius: px2rem(39);
        opacity: 0.05;
      }
    }
  }
}

.rtl-html {
  .r-1,
  .r-2 {
    &::before {
      left: 0;
    }
  }
}
</style>
