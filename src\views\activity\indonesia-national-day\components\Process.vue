<template>
  <div class="p-box">
    <div class="inner" :style="{ width: width }"></div>
  </div>
</template>
<script setup>
import { computed } from "vue";

const props = defineProps({
  max: {
    type: Number,
    default: 100,
  },
  current: {
    type: Number,
    default: 0,
  },
});

const width = computed(() => `${(props.current / props.max) * 100}%`);
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.p-box {
  width: 100%;
  height: px2rem(8);
  border-radius: px2rem(4);
  border: px2rem(1) solid #f96861;
  background: #660a06;
  position: relative;
  .inner {
    position: absolute;
    top: px2rem(-1);
    left: 0;
    height: px2rem(8);
    background: linear-gradient(90deg, #ffd78c 0%, #ffca1b 100%);
    border-radius: px2rem(4);
  }
}

.rtl-html {
  .p-box {
    .inner {
      left: unset;
      right: 0;
    }
  }
}
</style>
