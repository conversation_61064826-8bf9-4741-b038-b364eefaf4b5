<template>
  <div class="ranking">
    <div class="desc">
      {{ desc }}
    </div>
    <div class="top3">
      <div class="top-2">
        <div v-if="type === 3" class="room-cover">
          <img v-if="top2" :src="top2?.cover" />
          <div v-else class="empty-avatar">
            <svg-icon icon="vector" :size="20"></svg-icon>
          </div>
        </div>
        <div v-else class="avatar">
          <frame-avatar
            v-if="top2"
            :avatar="top2?.cover"
            :frame="top2?.frame"
            :size="40"
          ></frame-avatar>
          <div v-else class="empty-avatar">
            <svg-icon icon="vector" :size="20"></svg-icon>
          </div>
        </div>
        <div class="nickname">{{ top2?.name || "-" }}</div>
        <div class="coin-box">
          <GenderCoin :gender="type === 1 ? 1 : 2" :size="18"></GenderCoin>
          <gap :gap="3"></gap>
          <div class="coin-number">{{ formatCount(top2?.coin ?? 0) }}</div>
        </div>
      </div>
      <div class="top-1">
        <div v-if="type === 3" class="room-cover">
          <img v-if="top1" :src="top1?.cover" />
          <div v-else class="empty-avatar">
            <svg-icon icon="vector" :size="20"></svg-icon>
          </div>
        </div>
        <div v-else class="avatar">
          <frame-avatar
            v-if="top1"
            :avatar="top1?.cover"
            :frame="top1?.frame"
            :size="48"
          ></frame-avatar>
          <div v-else class="empty-avatar">
            <svg-icon icon="vector" :size="20"></svg-icon>
          </div>
        </div>
        <div class="nickname">{{ top1?.name || "-" }}</div>
        <div class="coin-box">
          <GenderCoin :gender="type === 1 ? 1 : 2" :size="18"></GenderCoin>
          <gap :gap="3"></gap>
          <div class="coin-number">{{ formatCount(top1?.coin ?? 0) }}</div>
        </div>
      </div>
      <div class="top-3">
        <div v-if="type === 3" class="room-cover">
          <img v-if="top3" :src="top3?.cover" />
          <div v-else class="empty-avatar">
            <svg-icon icon="vector" :size="20"></svg-icon>
          </div>
        </div>
        <div v-else class="avatar">
          <frame-avatar
            v-if="top3"
            :avatar="top3?.cover"
            :frame="top3?.frame"
            :size="40"
          ></frame-avatar>
          <div v-else class="empty-avatar">
            <svg-icon icon="vector" :size="20"></svg-icon>
          </div>
        </div>
        <div class="nickname">{{ top3?.name || "-" }}</div>
        <div class="coin-box">
          <GenderCoin :gender="type === 1 ? 1 : 2" :size="18"></GenderCoin>
          <gap :gap="3"></gap>
          <div class="coin-number">{{ formatCount(top3?.coin ?? 0) }}</div>
        </div>
      </div>
    </div>
    <div class="ranking-list" :class="`${hasMe ? 'pb103' : ''}`">
      <div
        class="rank-item"
        v-for="(item, index) in rankAfter3List"
        :key="index"
      >
        <div class="index">{{ item.rank }}</div>
        <gap :gap="8"></gap>
        <div v-if="type === 3" class="room-cover">
          <img v-if="item" :src="item?.cover" />
          <div v-else class="empty-avatar">
            <svg-icon icon="vector" :size="20"></svg-icon>
          </div>
        </div>
        <div v-else class="avatar">
          <frame-avatar
            :avatar="item?.cover"
            :frame="item.frame"
            :size="36"
          ></frame-avatar>
        </div>
        <gap :gap="8"></gap>
        <div class="nickname">{{ item.name || "-" }}</div>
        <gap :gap="8"></gap>
        <div class="coin-box">
          <GenderCoin :gender="type === 1 ? 1 : 2" :size="18"></GenderCoin>
          <gap :gap="3"></gap>
          <div class="coin-number">{{ formatCount(item?.coin ?? 0) }}</div>
        </div>
        <gap :gap="16"></gap>
      </div>
    </div>
    <div class="bottom-box" v-if="hasMe">
      <div class="rank-item">
        <div class="index">
          {{
            currentRank?.rank === -1
              ? "-"
              : (currentRank?.rank ?? 0) > 99
              ? "99+"
              : currentRank?.rank || "-"
          }}
        </div>
        <gap :gap="8"></gap>
        <div v-if="type === 3" class="room-cover">
          <img v-if="currentRank" :src="currentRank?.cover" />
          <div v-else class="empty-avatar">
            <svg-icon icon="vector" :size="20"></svg-icon>
          </div>
        </div>
        <div v-else class="avatar">
          <frame-avatar
            :avatar="currentRank?.cover"
            :frame="currentRank?.frame"
            :size="36"
          ></frame-avatar>
        </div>
        <gap :gap="8"></gap>
        <div class="nickname">{{ currentRank?.name || "-" }}</div>
        <div class="coin-box">
          <GenderCoin :gender="type === 1 ? 1 : 2" :size="18"></GenderCoin>
          <gap :gap="3"></gap>
          <div class="coin-number">{{ currentRank?.coin ?? 0 }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed, onMounted, ref } from "vue";
import GenderCoin from "@/components/gender-coin/index.vue";
import activityApi from "@/api/activity.js";
import { formatCount } from "@/utils/util.js";
const props = defineProps({
  desc: {
    type: String,
    default: "",
  },
  type: {
    type: Number,
    default: 1, // 1 送礼榜，2 收礼榜，3 房间榜
  },
});
const rankList = ref([]);
const top1 = computed(() => {
  return rankList.value.find((i) => i.rank === 1);
});
const top2 = computed(() => {
  return rankList.value.find((i) => i.rank === 2);
});
const top3 = computed(() => {
  return rankList.value.find((i) => i.rank === 3);
});
const rankAfter3List = computed(() => rankList.value.slice(3));
const currentRank = ref(null);
const hasMe = computed(
  () => currentRank.value && currentRank.value?.rank !== -1
);
const fetchData = async () => {
  if (props.type === 3) {
    const res = await activityApi.egypt_room_rank();
    if (res.code === 200) {
      rankList.value = (res.data?.items || []).map((item) => {
        return {
          name: item.room_name,
          cover: item.cover,
          coin: item.coin,
          rank: item.rank,
          frame: null,
        };
      });
      currentRank.value = {
        name: res.data?.user_rank?.room_name || "-",
        cover: res.data?.user_rank?.cover || "-",
        coin: res.data?.user_rank?.coin || 0,
        rank: res.data?.user_rank?.rank || -1,
        frame: null,
      };
    }
  } else {
    const res = await activityApi.egypt_rank({ type: props.type });
    if (res.code === 200) {
      rankList.value = (res.data?.items || []).map((item) => {
        return {
          name: item.nickname,
          cover: item.avatar,
          coin: item.coin,
          rank: item.rank,
          frame: item.frame,
        };
      });
      currentRank.value = {
        name: res.data?.user_rank?.nickname || "-",
        cover: res.data?.user_rank?.avatar || "-",
        coin: res.data?.user_rank?.coin || 0,
        rank: res.data?.user_rank?.rank || -1,
        frame: res.data?.user_rank?.frame || null,
      };
    }
  }
};

onMounted(() => {
  fetchData();
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.ranking {
  margin-top: px2rem(18);
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #5d0400;
  .desc {
    width: px2rem(254);
    padding: px2rem(10);
    border-radius: px2rem(12);
    background: rgba(255, 255, 255, 0.08);
    color: rgba(200, 83, 78, 0.7);
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(14);
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }
}

.coin-box {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  .coin-number {
    color: #dd5c00;
    font-family: Gilroy;
    font-size: px2rem(12);
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }
}
.top3 {
  width: 100%;
  height: px2rem(624);
  background-image: url("@/assets/images/activity/indonesia-national-day/bg-2.jpg");
  background-size: calc(100% + 1px) 100%;
  background-position: -1px 0;
  position: relative;
  .top-1,
  .top-2,
  .top-3 {
    position: absolute;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .avatar {
      width: px2rem(48);
      height: px2rem(48);
      margin-top: px2rem(76);
    }
    .room-cover {
      width: px2rem(48);
      height: px2rem(48);
      margin-top: px2rem(76);
      border: px2rem(2) solid rgba(255, 255, 255, 0.2);
      border-radius: px2rem(6);
      img {
        width: 100%;
        height: 100%;
        border-radius: px2rem(6);
        object-fit: cover;
      }
    }
    .nickname {
      margin-top: px2rem(38);
      color: #dd5c00;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(15);
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      width: px2rem(90);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .coin-box {
      margin-top: px2rem(4);
    }
  }
  .top-1 {
    width: px2rem(182);
    height: px2rem(242);
    left: 50%;
    top: px2rem(81);
    transform: translateX(-50%);
    background-image: url("@/assets/images/activity/indonesia-national-day/top1.png");
  }
  .top-2 {
    width: px2rem(160);
    height: px2rem(213);
    left: px2rem(23);
    top: px2rem(272);
    background-image: url("@/assets/images/activity/indonesia-national-day/top2.png");
    .avatar {
      width: px2rem(40);
      height: px2rem(40);
      margin-top: px2rem(64);
    }
    .room-cover {
      width: px2rem(42);
      height: px2rem(42);
      border-radius: px2rem(6);
      margin-top: px2rem(64);
      img {
        width: 100%;
        height: 100%;
        border-radius: px2rem(6);
        object-fit: cover;
      }
    }
    .nickname {
      margin-top: px2rem(36);
      color: #284799;
    }
    .coin-box {
      .coin-number {
        color: #284799;
      }
    }
  }
  .top-3 {
    width: px2rem(160);
    height: px2rem(213);
    right: px2rem(23);
    top: px2rem(272);
    background-image: url("@/assets/images/activity/indonesia-national-day/top3.png");
    .avatar {
      width: px2rem(40);
      height: px2rem(40);
      margin-top: px2rem(64);
    }
    .room-cover {
      width: px2rem(42);
      height: px2rem(42);
      border-radius: px2rem(6);
      margin-top: px2rem(64);
      img {
        width: 100%;
        height: 100%;
        border-radius: px2rem(6);
        object-fit: cover;
      }
    }
    .nickname {
      margin-top: px2rem(36);
      color: #8c5532;
    }
    .coin-box {
      .coin-number {
        color: #8c5532;
      }
    }
  }
}
.ranking-list {
  width: 100%;
  padding: 0 px2rem(15);
  margin-top: px2rem(-72);
  padding-bottom: px2rem(20);
  z-index: 3;
  .rank-item {
    display: flex;
    height: px2rem(60);
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    border-radius: px2rem(8);
    background: #740300;
    margin-bottom: px2rem(18);
    .index {
      width: px2rem(28);
      color: #fff;
      text-align: right;
      font-family: "DIN Next W1G";
      font-size: px2rem(17);
      font-style: italic;
      font-weight: 750;
      line-height: normal;
      flex-shrink: 0;
    }
    .avatar {
      display: flex;
      width: px2rem(46);
      height: px2rem(46);
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      position: relative;
    }
    .room-cover {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      position: relative;
      width: px2rem(46);
      height: px2rem(46);
      border: px2rem(2) solid rgba(255, 255, 255, 0.2);
      border-radius: px2rem(6);
      img {
        width: 100%;
        height: 100%;
        border-radius: px2rem(6);
        object-fit: cover;
      }
    }

    .nickname {
      color: var(---White, #fff);

      /* T13/R */
      font-family: Gilroy;
      font-size: px2rem(13);
      font-style: normal;
      font-weight: 500;
      line-height: 124%; /* 16.12px */
      flex-grow: 1;
    }
    .coin-number {
      color: #ffda6d;
      font-family: "DIN Next W1G";
      font-size: px2rem(16);
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }
  }
}
.pb103 {
  padding-bottom: px2rem(103);
}
.rtl-html {
  .rank-item {
    .index {
      text-align: left;
    }
  }
}

.bottom-box {
  width: px2rem(391);
  height: px2rem(103);
  flex-shrink: 0;
  position: fixed;
  left: 0;
  bottom: px2rem(-12);
  z-index: 3;
  border-radius: px2rem(24) px2rem(24) 0px 0px;
  background: linear-gradient(180deg, #9d0900 0%, #6c0600 100%);
  &::after {
    content: "";
    width: px2rem(391);
    height: px2rem(103);
    position: absolute;
    left: 0;
    bottom: 0;
    background-image: url("@/assets/images/activity/recharge-ranking/bottom-bg.png");
    background-size: 100% 100%;
  }
  .rank-item {
    display: flex;
    align-items: center;
    margin-left: px2rem(15);
    margin-right: px2rem(33);
    margin-top: px2rem(11);
    .index {
      width: px2rem(28);
      color: #fff;
      text-align: right;
      font-family: "DIN Next W1G";
      font-size: px2rem(17);
      font-style: italic;
      font-weight: 750;
      line-height: normal;
    }
    .avatar {
      display: flex;
      width: px2rem(46);
      height: px2rem(46);
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      position: relative;
    }
    .room-cover {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      position: relative;
      width: px2rem(46);
      height: px2rem(46);
      border: px2rem(2) solid rgba(255, 255, 255, 0.2);
      border-radius: px2rem(6);
      img {
        width: 100%;
        height: 100%;
        border-radius: px2rem(6);
        object-fit: cover;
      }
    }
    .nickname {
      color: var(---White, #fff);

      /* T13/R */
      font-family: Gilroy;
      font-size: px2rem(13);
      font-style: normal;
      font-weight: 500;
      line-height: 124%; /* 16.12px */
      flex-grow: 1;
    }

    .coin-box {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      margin-top: px2rem(-9);
      .coin-number {
        color: #ffda6d;
        font-family: "DIN Next W1G";
        font-size: px2rem(16);
        font-style: normal;
        font-weight: 700;
        line-height: 1.2;
        letter-spacing: -0.5px;
      }
    }
  }
  .bottom-desc {
    display: flex;
    height: px2rem(28);
    flex-direction: column;
    justify-content: center;

    position: absolute;
    right: px2rem(18);
    top: px2rem(43);
    text-align: right;
    font-family: Gilroy;
    font-size: 10px;
    font-style: normal;
    font-weight: 500;
    line-height: 100%; /* 10px */
    & > div:first-child {
      color: #ff9791;
    }
    & > div:last-child {
      color: #cc5c55;
      margin-top: px2rem(8);
    }
  }
}
</style>
