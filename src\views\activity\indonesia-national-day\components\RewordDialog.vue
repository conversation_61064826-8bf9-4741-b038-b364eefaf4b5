<template>
  <van-overlay
    class-name="prize-dialog"
    :show="showDialog"
    @touchmove.prevent
    z-index="1001"
  >
    <div v-if="showDialog" class="base-modal-wrapper" @click.stop>
      <div class="box">
        <div class="bg-3"></div>
        <div class="bg-4"></div>
        <GoldBox>
          <div class="title">
            <BlockTitle
              :title="$t('indonesia.congratulations')"
              :iconGap="0"
            ></BlockTitle>
          </div>
          <div class="rewards">
            <div class="reward" v-for="prize in prizes">
              <div class="cover">
                <PrizeBox :rewards="prize"></PrizeBox>
              </div>
              <div class="name">{{ prize.reward_name }}</div>
            </div>
          </div>
          <div class="desc">
            {{ $t("indonesia.task_reward_sent") }}
          </div>
          <div class="action">
            <div class="btn-confirm" @click="showDialog = false">
              <span>{{ $t("indonesia.ok") }}</span>
            </div>
          </div>
        </GoldBox>
      </div>
      <div class="close" @click="showDialog = false"></div>
    </div>
  </van-overlay>
</template>
<script setup>
import { ref } from "vue";
import GoldBox from "./GoldBox.vue";
import BlockTitle from "./BlockTitle.vue";
import PrizeBox from "./PrizeBox.vue";

const showDialog = ref(false);
const prizes = ref([]);

const handleOpen = (data) => {
  prizes.value = data || [];
  showDialog.value = true;
};

defineExpose({
  open: handleOpen,
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.prize-dialog {
  --van-overlay-background: rgba(0, 0, 0, 0.88);
}
@keyframes show {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.base-modal-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .box {
    width: px2rem(331);
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
    position: relative;
    .bg-3 {
      position: absolute;
      top: px2rem(-30);
      width: px2rem(120);
      height: px2rem(65);
      z-index: 5;
      flex-shrink: 0;
      background-image: url("@/assets/images/activity/indonesia-national-day/bg-3.png"); /* 中间部分的图片 */
      background-size: 100% 100%;
    }
    .bg-4 {
      position: absolute;
      top: px2rem(-184);
      width: px2rem(298);
      height: px2rem(298);
      flex-shrink: 0;
      background-image: url("@/assets/images/activity/indonesia-national-day/bg-4.png"); /* 中间部分的图片 */
      background-size: 100% 100%;
    }
    .title {
      margin-top: px2rem(16);
    }
    .desc {
      margin-top: px2rem(6);
      color: #c8534e;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(14);
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      width: px2rem(217);
    }
    .rewards {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: px2rem(23) 0;
      .reward {
        display: flex;
        align-items: center;
        flex-direction: column;
        width: px2rem(87);
        .cover {
          width: px2rem(70);
          height: px2rem(70);
        }
        .name {
          margin-top: px2rem(12);
          width: px2rem(87);
          color: #ffa19d;
          text-align: center;
          font-family: Gilroy;
          font-size: px2rem(14);
          font-style: normal;
          font-weight: 500;
          line-height: 100%; /* 14px */
        }
      }
    }

    .action {
      margin-top: px2rem(20);
      display: flex;
      align-items: center;
      justify-content: center;
      .btn-confirm {
        position: relative;
        z-index: 9;
        width: px2rem(132);
        height: px2rem(34);
        margin-bottom: px2rem(22);
        flex-shrink: 0;
        border-radius: px2rem(50);
        border: 1.5px solid #fff;
        background: linear-gradient(
          180deg,
          #ffad9e 0%,
          #d90000 47.12%,
          #830303 83.17%
        );
        box-shadow: 0px -4px 4px 0px rgba(104, 2, 2, 0.64) inset,
          0px 3px 3px 0px rgba(255, 255, 255, 0.66) inset;
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          text-align: center;
          text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
          font-family: Gilroy;
          font-size: px2rem(18);
          font-style: normal;
          font-weight: 700;
          line-height: 100%; /* 18px */
          background: linear-gradient(180deg, #fff 14.33%, #ffc832 88.73%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
}

.close {
  width: px2rem(40);
  height: px2rem(40);
  font-size: px2rem(40);
  margin-top: px2rem(33);
  color: #ffe8c3;
  background-image: url("@/assets/images/activity/recharge-ranking/dialog-close.svg"); /* 中间部分的图片 */
  background-size: 100% 100%;
}
</style>
