<template>
  <div class="app-container flex daily-ranking">
    <header>
      <header-bar font-color="#fff" close-type="close" />
      <div class="tab-wrap flex" ref="tabWrapRef">
        <div
          class="tab"
          :class="{ active: currentTabIdx === idx }"
          v-for="(tab, idx) in tabList"
          :key="idx"
          @click="onChangeTab(idx)"
        >
          {{ tab }}
        </div>
      </div>
    </header>

    <swiper
      class="main"
      :dir="['ar'].includes(i18n.global.locale) ? 'rtl' : 'ltr'"
      :modules="modules"
      slides-per-view="auto"
      :space-between="0"
      @swiper="setControlledSwiper"
      @slideChange="onSlideChange"
    >
      <swiper-slide class="swipe-item" v-for="(n, idx) in tabList" :key="idx">
        <div class="rule-cell cell">
          <div class="rule-title flex">
            <img
              class="front-dot"
              src="@/assets/images/daily-ranking/dot-title-left.svg"
              alt=""
            />
            <!-- <gap :gap="16"></gap> -->
            <span class="ellipsis">{{
              $t("base_total.daily_ranking_rule_title")
            }}</span>
            <!-- <gap :gap="16"></gap> -->
            <img
              class="back-dot"
              src="@/assets/images/daily-ranking/dot-title-right.svg"
              alt=""
            />
          </div>
          <div class="rule-desc">
            <div
              class="rule-desc-text flex"
              v-for="(text, idx) in ruleList[idx]"
              :key="idx"
            >
              <div class="tips-step"></div>
              <div>{{ text }}</div>
            </div>
          </div>
        </div>
        <template v-for="(_, rankName) in rankData" :key="rankName">
          <div
            class="rule-cell cell"
            v-if="rankData[rankName]?.list.length > 0"
          >
            <div class="rule-title flex">
              <img
                class="front-dot"
                src="@/assets/images/daily-ranking/dot-title-left.svg"
                alt=""
              />
              <!-- <gap :gap="16"></gap> -->
              <span>{{
                $t("base_total.daily_ranking_reward_description")
              }}</span>
              <!-- <gap :gap="16"></gap> -->
              <img
                class="back-dot"
                src="@/assets/images/daily-ranking/dot-title-right.svg"
                alt=""
              />
            </div>
            <div class="rule-desc">
              <div class="rule-desc-text flex">
                <div class="tips-step"></div>
                <div>
                  {{
                    $t(
                      `base_total.daily_ranking_${rankName.replace(
                        "Ranks",
                        ""
                      )}_reward`
                    )
                  }}
                </div>
              </div>
              <div class="rule-desc-detail">{{ rankData[rankName]?.desc }}</div>
            </div>
            <div class="reward-wrap">
              <div
                class="reward-item flex"
                v-for="(item, idx) in rankData[rankName]?.list"
              >
                <div class="cell-wrap sort flex" :style="{ color: item.color }">
                  Top{{ idx + 1 }}
                </div>
                <div
                  class="cell-wrap flex"
                  v-for="(subItem, subIdx) in item?.rewardInfo"
                  :key="subIdx"
                >
                  <img :src="subItem?.icon" alt="" />
                  <div class="reward-name">{{ subItem?.name }}</div>
                  <div class="reward-time">{{ subItem?.intr }}</div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </swiper-slide>
    </swiper>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import { useRoute } from "vue-router";
import { i18n } from "@/i18n/index.js";
import { Swiper, SwiperSlide } from "swiper/vue";
import { Controller } from "swiper/modules";
import baseForAppApi from "@/api/baseforapp.js";

const t = i18n.global.t;
const route = useRoute();

const modules = [Controller];
const controlledSwiper = ref(null);
const tabWrapRef = ref(null);
const setControlledSwiper = (swiper) => {
  controlledSwiper.value = swiper;
};

// 1魅力榜 2财富榜 3房间榜 4cp榜   7家族榜
const tabList = ref([
  t("base_total.daily_ranking_charm_ranking"),
  t("base_total.daily_ranking_wealth_ranking"),
  t("base_total.daily_ranking_room_ranking"),
  t("base_total.daily_ranking_cp_ranking"),
  t("base_total.siya_20250804_39e63e30cff430b4176b"),
]);

const currentTabIdx = ref(0);
const onChangeTab = (idx, isFirst = false) => {
  controlledSwiper.value.slideTo(idx, isFirst ? 0 : 200);
  scrollToTab(idx); 
};
const onSlideChange = (swiper) => {
  const idx = swiper.activeIndex;
  if (idx === currentTabIdx.value) return;
  onChangeSwipe(idx);
};
const onChangeSwipe = (n) => {
  currentTabIdx.value = n;
  scrollToTab(n);
  getRankActivity();
};
const scrollToTab = (idx) => {
  nextTick(() => {
    if (!tabWrapRef.value) return;
    
    const tabs = tabWrapRef.value.querySelectorAll('.tab');
    if (tabs[idx]) {
      const tab = tabs[idx];
      const containerScrollWidth = tabWrapRef.value.scrollWidth;
      const containerWidth = tabWrapRef.value.offsetWidth;
      const tabLeft = tab.offsetLeft;
      const tabWidth = tab.offsetWidth;
      
      // 计算需要滚动的位置，使选中的tab居中
      const scrollLeft = tabLeft - (containerWidth - tabWidth) / 2;
      
      // 处理边界情况
      const maxScrollLeft = containerScrollWidth - containerWidth;
      const finalScrollLeft = Math.max(0, Math.min(scrollLeft, maxScrollLeft));
      
      // 平滑滚动到指定位置
      tabWrapRef.value.scrollTo({
        left: finalScrollLeft,
        behavior: 'smooth'
      });
    }
  });
};

const UTCOffset = ref("+0");
const defaultRuleList = () => {
  return [
    [
      t("base_total.daily_ranking_charm_ranking_rule_1"),
      t("base_total.daily_ranking_charm_ranking_rule_2", [UTCOffset.value]),
      t("base_total.daily_ranking_charm_ranking_rule_3"),
    ],
    [
      t("base_total.daily_ranking_wealth_ranking_rule_1"),
      t("base_total.daily_ranking_charm_ranking_rule_2", [UTCOffset.value]),
      t("base_total.daily_ranking_wealth_ranking_rule_2"),
    ],
    [
      t("base_total.daily_ranking_room_ranking_rule_1"),
      t("base_total.daily_ranking_charm_ranking_rule_2", [UTCOffset.value]),
      t("base_total.daily_ranking_room_ranking_rule_2"),
    ],
    [
      t("base_total.daily_ranking_cp_ranking_rule_1"),
      t("base_total.daily_ranking_charm_ranking_rule_2", [UTCOffset.value]),
      t("base_total.daily_ranking_cp_ranking_rule_2"),
    ],
    [
      t("base_total.siya_20250804_3c51aca848c495ab9bf6"),
      t("base_total.daily_ranking_charm_ranking_rule_2", [UTCOffset.value]),
      t("base_total.siya_20250804_1ba7032b1a642cabe7f8"),
    ],
  ];
};
const ruleList = ref([]);
const rankData = ref([]);

const getRankInfo = async () => {
  const response = await baseForAppApi.rank_info();
  if (response.code === 200) {
    UTCOffset.value = response.data.offset;
    ruleList.value = defaultRuleList();
  }
};

const getRankActivity = async () => {
  let tab = currentTabIdx.value + 1;
  if(tab === 5) tab = 7
  const response = await baseForAppApi.rank_activity({
    rank_type: tab,
  });
  if (response.code === 200) {
    rankData.value = response.data;
  }
};

onMounted(() => {
  // tab:  1 魅力; 2 财富; 3 房间; 4 cp  7 家族榜
  const tab = Number(route.query.tab);
  if (tab) onChangeTab(tab - 1, true);
  getRankInfo();
  if (tab == 1 || !tab) getRankActivity();
});
</script>

<style scoped lang="scss">
@import "@/assets/scss/common.scss";

$pageBgColor: #190f2c;

$borderStyle: 0.5px solid #ffffff1f;

.app-container {
  flex-direction: column;
  height: 100vh;
  background-color: $pageBgColor;
}

header {
  width: 100%;
}

.tab-wrap {
  padding: $gap6 $gap16;
  border-bottom: 1px solid #ffffff1f;
  overflow-x: scroll;
  overflow-y: hidden;
  height: px2rem(40);
  display: flex;
  flex-wrap: nowrap;
  &::-webkit-scrollbar {
    display: none; /* Chrome/Safari/Opera */
  }

  .tab {
    height: px2rem(24);
    color: rgba($white, 0.7);
    padding: 0 px2rem(12);
    white-space: nowrap;

    @include fontSize15;
    @include centerV;

    &.active {
      font-weight: $fontWeightBold;
      color: $white;

      @include fontSize17;
    }
  }
}

.main {
  flex: 1;
  width: 100vw;
  overflow: hidden;
}

.swipe-item {
  overflow-y: scroll;
  height: 100%;
  color: $white;
  padding-bottom: $pageBottomPadding;
}

.cell {
  padding: $gap32 $gap16 0;

  &.rule-cell {
    // position: sticky;
    // top: 0;
    background-color: $pageBgColor;
  }

  .rule-title {
    justify-content: center;
    margin-bottom: $gap4;
    font-weight: $fontWeightBold;
    text-align: center;

    @include fontSize17;

    .front-dot,
    .back-dot {
      width: px2rem(34);
      height: px2rem(8);
    }
    span {
      padding: 0 px2rem(16);
      max-width: 65%;
      display: inline-block;
    }
  }

  .rule-desc-text {
    flex-wrap: nowrap;
    align-items: stretch;
    margin-bottom: $gap14;

    @include fontSize13;

    .tips-step {
      flex: none;
      width: px2rem(4);
      height: px2rem(4);
      margin: $gap6 $gap8;
      background-color: $white;
      border-radius: 50%;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
  .rule-desc-detail {
    font-size: px2rem(13);
    color: #ffffff80;
    margin-top: px2rem(-8);
    word-break: break-all;
  }

  .reward-wrap {
    border-radius: px2rem(8);
    border: $borderStyle;
    margin-top: px2rem(16);
    .reward-item {
      @include fontSize11;
      border-bottom: $borderStyle;
      font-weight: 500;
      .cell-wrap {
        flex: 1;
        height: px2rem(100);
        flex-direction: column;
        justify-content: center;
        border-left: $borderStyle;
        min-width: 0;
        img {
          width: px2rem(54);
          height: px2rem(54);
        }
        .reward-name {
          padding: px2rem(2) px2rem(4);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 100%;
        }
        .reward-time {
          color: #ffffff80;
        }
        &:first-child {
          border-left: none;
        }
      }
      .sort {
        width: px2rem(60);
        font-size: px2rem(14);
        flex: none;
        font-weight: 700;
      }
      &:last-child {
        border-bottom: none;
      }
    }
  }
}

::v-deep(.header-content .title) {
  text-align: center;
}
</style>
