<template>
  <div class="custom-tabs">
    <div class="tabs-nav" ref="navRef">
      <template v-for="(tab, index) in tabs" :key="tab.name">
        <div
          class="tab-item"
          :class="{ active: activeTab === index }"
          @click="handleTabClick(index)"
        >
          {{ tab.title }}
        </div>
        <gap :gap="colunmGap"></gap>
      </template>
      <div v-if="showActiveLine" class="tab-line" :style="lineStyle"></div>
    </div>
  </div>
</template>

<script setup>
import {
  ref,
  computed,
  onMounted,
  onUnmounted,
  watch,
  getCurrentInstance,
  nextTick,
} from "vue";
const { proxy } = getCurrentInstance();
const lang = proxy.$languageFile;

const props = defineProps({
  modelValue: {
    type: Number,
    default: 0,
  },
  tabItems: {
    type: Array,
    default: () => [],
  },
  showActiveLine: {
    type: Boolean,
    default: true,
  },
  colunmGap: {
    type: Number,
    default: 8,
  },
  width: {
    type: [Number, String],
    default: "100%",
  },
  fontSize: {
    type: [Number, String],
    default: 17,
  },
});

const emit = defineEmits(["tab-change", "update:modelValue"]);

const activeTab = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit("update:modelValue", value);
  },
});
const navRef = ref(null);
const tabWidths = ref([]);
const tabOffsets = ref([]);

const tabs = ref(props.tabItems.length > 0 ? props.tabItems : []);

const lineStyle = computed(() => {
  if (tabWidths.value.length === 0 || tabOffsets.value.length === 0) {
    return {};
  }
  return {
    width: `${tabWidths.value[activeTab.value]}px`,
    transform: `translateX(${tabOffsets.value[activeTab.value]}px)`,
  };
});

const realWidth = computed(() => {
  if (!isNaN(`${props.width}`)) {
    return proxy.$pxToRemPx(props.width);
  }
  return props.width;
});

const realFontSize = computed(() => {
  if (!isNaN(`${props.fontSize}`)) {
    return `${proxy.$pxToRemPx(props.fontSize)}px`;
  }
  return props.fontSize;
});

const realColunmGap = computed(() => {
  return proxy.$pxToRemPx(props.colunmGap);
});

const handleTabClick = (index) => {
  activeTab.value = index;
  emit("tab-change", {
    index,
    tab: tabs.value[index],
  });
};

const calculateTabMetrics = () => {
  if (!navRef.value) return;

  const tabElements = navRef.value.querySelectorAll(".tab-item");
  const widths = [];
  const offsets = [];
  let currentOffset = 0;

  tabElements.forEach((tab, index) => {
    const width = tab.offsetWidth;
    widths.push(width);
    offsets.push(currentOffset);
    currentOffset += width + realColunmGap.value;
  });

  tabWidths.value = widths;
  if (["ar", "ur"].includes(lang)) {
    tabOffsets.value = offsets.reverse();
  } else {
    tabOffsets.value = offsets;
  }
};

onMounted(() => {
  nextTick(() => {
    setTimeout(calculateTabMetrics, 300);
    window.addEventListener("resize", calculateTabMetrics);
  });
});

watch(
  () => props.tabItems,
  (newItems) => {
    if (newItems.length > 0) {
      tabs.value = newItems;
      // Recalculate metrics after DOM update
      setTimeout(calculateTabMetrics, 0);
    }
  },
  { deep: true }
);

onUnmounted(() => {
  window.removeEventListener("resize", calculateTabMetrics);
});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.custom-tabs {
  width: v-bind(realWidth);
  overflow-x: auto;
  .tabs-nav {
    position: relative;
    display: flex;
    height: px2rem(32);

    .tab-item {
      flex: 1;
      display: flex;
      flex-shrink: 0;
      flex-wrap: nowrap;
      white-space: nowrap;
      align-items: flex-start;
      justify-content: center;
      color: #af8080;
      text-align: center;
      transition: all 0.5s;

      /* T17/B */
      font-family: Gilroy;
      font-size: v-bind(realFontSize);
      font-style: normal;
      font-weight: 700;
      line-height: normal;

      &.active {
        color: #731818;
      }
    }

    .tab-line {
      position: absolute;
      bottom: 0;
      left: 0;
      height: px2rem(3);
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      display: flex;
      justify-content: center;
      align-items: center;
      &::after {
        content: "";
        height: px2rem(3);
        border-radius: px2rem(3);
        width: px2rem(64);
        background-color: #731818;
      }
    }
  }

  .tabs-content {
    position: relative;

    .tab-pane {
      display: none;

      &.active {
        display: block;
      }
    }
  }
}
</style>
