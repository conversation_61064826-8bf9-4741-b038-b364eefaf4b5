import xlsx from "node-xlsx";
import fs from "fs";
import path from "path";
import axios from "axios";
import { fileURLToPath } from "url";
import dialog from "node-file-dialog";
import readline from "readline";

const http = axios.create();
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const OUTPUT_DIR = path.join(__dirname, ...["src", "i18n"]); // 统一输出目录
const DEFAULT_TEMPLATE = "en"; // 默认模板语言
const API_MODE = 1; // 1: task_id；2: release_id
const API_EXTRA_PARAMS = {
  namespace: "WEB",
  // api参数有需要自己添加，优先级比输入高
};
// const API_URL = "https://translate.ilivedata.com/api/v1/resource";
const API_URL = "http://121.41.115.227:8005/api/v1/resource";
const KEY_MAP = {
  key: 0,
  zh_cn: 1,
  en: 2,
  ar: 3,
  tr: 4,
  pt: 5,
  zh_tw: 6,
  id: 7,
  es: 8,
  hi: 9,
  bn: 10,
  ur: 11,
};
const LANG_ROW = 11;
const languageFileMap = {
  "zh-CN": "zh_cn",
  "zh-TW": "zh_tw",
  en: "en",
  ar: "ar",
  tr: "tr",
  pt: "pt",
  id: "id",
  es: "es",
  ur: "ur",
  hi: "hi",
  bn: "bn",
};

class I18n {
  constructor() {
    this.langData = {};
    this.excelPath = "";
    this.langDir = "";
    this.excelFile = null;
    this.langLog = {};
    this.langKeys = {};
    this.rl = null;
  }

  async excelTranslate() {
    try {
      Object.keys(KEY_MAP).forEach((lang) => {
        if (lang === "key") return;
        this.langData[lang] = {};
        this.langLog[lang] = {
          fileStatus: "", // 文件状态: create ,update, noChange
          repeatKeys: [], // 重复的key
          newKeyLength: 0, // 新增的key数量
          oldKeyLength: 0, // 旧的的key数量
        };
      });
      this.excelFile = xlsx.parse(this.excelPath);
      this.excelFile.forEach((sheet) => {
        const sheetData = sheet.data;
        // 解析每个 sheet 的数据
        sheetData.forEach((row, index) => {
          const key = row[KEY_MAP.key];
          if (!key || index === 0) return;
          Object.keys(KEY_MAP).forEach((lang) => {
            if (lang === "key") return;
            this.langData[lang][key] = row[KEY_MAP[lang]];
          });
        });
      });
      const inputLang = await this.question(
        this.rl,
        "请输入JSON格式的文件名（如 anchor，或者 activity/anchor ）: "
      );
      if (!inputLang) {
        console.error(`错误：请输入文件名`);
        return;
      }

      Object.keys(this.langData).forEach((lang) => {
        const langSubDir = path.join(this.langDir, lang);
        if (!fs.existsSync(langSubDir)) fs.mkdirSync(langSubDir);
        const targetFile = path.join(
          langSubDir,
          ...`${inputLang}.json`.split("/")
        );
        // 1. 读取原始文件内容（作为字符串完整保留）
        let originalData = {};
        if (fs.existsSync(targetFile)) {
          try {
            this.langLog[lang]["fileStatus"] = "update";
            originalData = JSON.parse(fs.readFileSync(targetFile, "utf-8"));
          } catch (e) {
            console.warn(`解析 ${targetFile} 失败，将创建新文件`);
          }
        } else {
          this.langLog[lang]["fileStatus"] = "create";
        }
        const newData = {};
        Object.keys(this.langData[lang]).forEach((key) => {
          // key 为空或者keys，或者值是空，跳过
          if (key === "keys" || !key) return;
          if (originalData.hasOwnProperty(key)) {
            this.langLog[lang]["repeatKeys"].push(key);
          } else {
            newData[key] = this.langData[lang][key];
          }
        });
        this.langLog[lang]["newKeyLength"] = Object.keys(newData).length;
        this.langLog[lang]["oldKeyLength"] = Object.keys(originalData).length;

        if (Object.keys(newData).length === 0) {
          this.langLog[lang]["fileStatus"] = "noChange";
        } else {
          fs.writeFileSync(
            targetFile,
            JSON.stringify({ ...originalData, ...newData }, null, 2)
          );
        }
      });
      console.log(
        "翻译文件已生成到 src/i18n/lang 目录下的各语言子目录中,辛苦手动格式化，生成情况如下："
      );
      console.log("------------------------------------------>>>");
      console.log(this.langLog);
      console.log("<<<------------------------------------------");
    } catch (error) {
      console.error("文件解析失败:", error);
    }
  }

  async addDataByApi() {
    try {
      const resData = await this.apiQuestion();
      const inputPath = await this.pathQuestion();
      const keysList = await this.keysQuestion();

      if (keysList[0] === "all") {
        this.langData = resData;
      } else {
        this.langData = resData.map((langItem) => {
          const { resource, language } = langItem;
          const newResource = {};
          keysList.forEach((key) => {
            if (resource.hasOwnProperty(key)) newResource[key] = resource[key];
          });
          return { language, resource: newResource };
        });
      }

      this.langData.forEach((langItem) => {
        const { language: originalLang, resource } = langItem;
        const language = languageFileMap[originalLang];

        this.langLog[language] = {
          repeatKeys: [], // 重复的key
          newKeyLength: 0, // 新增的key数量
          oldKeyLength: 0, // 旧的的key数量
        };
        const targetJsonDir = path.join(
          this.langDir,
          language,
          ...`${inputPath}.json`.split("/")
        );
        let originalData = {};
        const newData = {};
        if (fs.existsSync(targetJsonDir)) {
          console.log(targetJsonDir);
          originalData = JSON.parse(fs.readFileSync(targetJsonDir, "utf-8"));
          this.langLog[language]["oldKeyLength"] =
            Object.keys(originalData).length;
        } else {
          // fs.mkdirSync(targetJsonDir);
        }

        Object.keys(resource).forEach((key) => {
          if (originalData.hasOwnProperty(key)) {
            this.langLog[language]["repeatKeys"].push(key);
          } else {
            newData[key] = resource[key];
            originalData[key] = resource[key];
          }
        });
        this.langLog[language]["newKeyLength"] = Object.keys(newData).length;
        if (this.langLog[language]["newKeyLength"] > 0) {
          fs.writeFileSync(
            targetJsonDir,
            JSON.stringify({ ...originalData, ...newData }, null, 2)
          );
        }
      });
      console.log(
        "翻译文件已生成到 src/i18n/lang 目录下的各语言子目录中，生成情况如下："
      );
      console.log("------------------------------------------>>>");
      console.log(this.langLog);
      console.log("<<<------------------------------------------");
    } catch (error) {
      console.log(error);
    }
  }

  async updateDataByApi() {
    try {
      const resData = await this.apiQuestion();
      const inputPath = await this.pathQuestion();
      const inputLangList = await this.langQusestion();
      let keysList = await this.keysQuestion();

      this.langData = resData;
      this.langData.forEach((langItem) => {
        const { language: originalLang, resource } = langItem;
        const language = languageFileMap[originalLang];
        if (!inputLangList.includes(language)) return;
        const targetJsonDir = path.join(
          this.langDir,
          language,
          ...`${inputPath}.json`.split("/")
        );
        let originalData = JSON.parse(fs.readFileSync(targetJsonDir, "utf-8"));
        if (keysList[0] === "all") keysList = Object.keys(originalData);
        keysList.forEach((key) => {
          if (originalData.hasOwnProperty(key) && resource[key]) {
            originalData[key] = resource[key];
          }
        });
        fs.writeFileSync(targetJsonDir, JSON.stringify(originalData, null, 2));
      });
      console.log("修改成功");
    } catch (error) {
      console.log(error);
    }
  }

  async createNewLang() {
    try {
      const inputValue = await this.question(
        this.rl,
        "请输入要创建的语种（如 en）: "
      );

      if (!inputValue) {
        console.error(`错误：请输入语种`);
        return;
      }
      this.excelFile = xlsx.parse(this.excelPath);
      this.excelFile.forEach((sheet) => {
        const sheetData = sheet.data;
        // 解析每个 sheet 的数据
        sheetData.forEach((row) => {
          if (row.length === 0 || !row[0]) return;
          const parseKey = row[0].replace(/\"/g, "").trim();
          const parseValue = row[LANG_ROW].replace(/\"/g, "");
          this.langData[parseKey] = parseValue;
        });
      });
      if (!fs.existsSync(path.join(this.langDir, inputValue)))
        fs.mkdirSync(path.join(this.langDir, inputValue));

      this.copyAndWrireFile(
        path.join(this.langDir, DEFAULT_TEMPLATE),
        path.join(this.langDir, inputValue)
      );
      console.log("语言创建成功");
    } catch (error) {
      console.error("处理文件时出错:", error);
    }
  }

  copyAndWrireFile(copyPath, targetPath) {
    const files = fs.readdirSync(copyPath);
    files.forEach((file) => {
      const curCopyPath = path.join(copyPath, file);
      const curTargetPath = path.join(targetPath, file);

      if (!file.endsWith(".json")) {
        if (!fs.existsSync(curTargetPath)) fs.mkdirSync(curTargetPath);
        this.copyAndWrireFile(curCopyPath, curTargetPath);
      } else {
        const originalContent = fs.readFileSync(curCopyPath, "utf-8");
        const originalData = JSON.parse(originalContent);
        Object.keys(originalData).forEach((key) => {
          if (this.langData.hasOwnProperty(key)) {
            originalData[key] = this.langData[key];
          } else {
            originalData[key] = "";
          }
        });
        fs.writeFileSync(curTargetPath, JSON.stringify(originalData, null, 2));
      }
    });
  }

  async selectExcelFile() {
    return new Promise(async (resolve, reject) => {
      try {
        const filePaths = await dialog({
          type: "open-file",
          title: "请选择 Excel 文件",
          filters: [{ name: "Excel 文件", extensions: ["xlsx", "xls"] }],
        });
        if (!filePaths.length) {
          console.log("操作已取消");
          reject("操作已取消");
          return;
        }
        this.excelPath = filePaths[0];
        resolve(this.excelPath);
      } catch (err) {
        console.error("文件选择失败:", err.message);
      }
    });
  }

  parseValue(value) {
    let parseValue = null;
    const regex = /\s*[\[\]【】]\s*/;
    const list = value.split("\n");
    if (list.length === 1) {
      parseValue = list[0].trim();
    } else {
      parseValue = list
        .filter((item) => !regex.test(item) && item)
        .map((item) => item.trim());
    }
    return parseValue;
  }

  async parseJsonData() {
    const targetJsonDir = path.join(this.langDir, "test.json");
    const params = {
      release_id: "",
      ...API_EXTRA_PARAMS,
    };
    const { data } = await http.get(API_URL, { params });
    const targetLang = data.data.items.find((item) => item.language === "hi");
    const originalData = targetLang.resource;
    const newData = {};
    Object.keys(originalData).forEach((key) => {
      let parseValue = "";
      const regex = /\s*[\[\]【】]\s*/;
      const list = originalData[key].split("\n");
      if (list.length === 1) {
        parseValue = list[0].trim();
      } else {
        parseValue = list
          .filter((item) => !regex.test(item) && item)
          .map((item) => item.trim());
      }
      newData[key] = parseValue;
    });

    fs.writeFileSync(targetJsonDir, JSON.stringify(newData, null, 2));
    // const newContent = JSON.stringify(originalData, null, 2);
    // fs.writeFileSync(targetJsonDir, newContent);
  }

  async showMainMenu() {
    try {
      console.log("\n请选择操作:");
      console.log("1. api导入【新增】");
      console.log("2. api导入【修改】");
      console.log("3. excel文件导入【新增】");
      console.log("4. 创建新的语种");
      console.log("0. 退出");

      const choice = await this.question(this.rl, "请输入选项数字: ");

      switch (choice) {
        case "1":
          await this.addDataByApi();
          break;
        case "2":
          await this.updateDataByApi();
          break;
        case "3":
          await this.selectExcelFile();
          await this.excelTranslate();
          break;
        case "4":
          await this.selectExcelFile();
          await this.createNewLang();
          break;
        case "100": // 自定义脚本
          this.parseJsonData();
          break;
        default:
          this.exit();
      }
    } finally {
      this.rl.close();
    }
  }

  async start() {
    this.rl = this.createReadline();
    this.langDir = path.join(OUTPUT_DIR, "lang");
    await this.showMainMenu();
  }

  createReadline() {
    return readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
  }

  async question(rl, prompt) {
    return new Promise((resolve) => rl.question(prompt, resolve));
  }

  apiQuestion() {
    return new Promise(async (resolve, reject) => {
      try {
        const inputReleaseId = await this.question(this.rl, "请输入id: ");
        const params = {
          [API_MODE === 1 ? "task_id" : "release_id"]:
            inputReleaseId.trim() || "",
          ...API_EXTRA_PARAMS,
        };
        const { data } = await http.get(API_URL, { params });
        if (data.code !== 200) throw new Error("请求失败");
        resolve(data.data.items);
      } catch (error) {
        console.log(error);
        reject(error);
      }
    });
  }
  pathQuestion() {
    return new Promise(async (resolve, reject) => {
      try {
        const promptMessage =
          "请输入JSON文件的路径（例如：anchor 或 activity/anchor）: ";
        const filePath = await this.question(this.rl, promptMessage);
        if (!filePath?.trim()) {
          reject(new Error("文件路径不能为空，请重新输入！"));
          return;
        }
        resolve(filePath.trim());
      } catch (error) {
        console.error("获取文件路径时出错:", error);
        reject(error);
      }
    });
  }

  keysQuestion() {
    return new Promise(async (resolve, reject) => {
      try {
        const promptMessage =
          [
            "请输入需要修改的键（多个键用逗号分隔，例如：key1,key2）",
            "如需修改所有键，请直接按回车",
          ].join("\n") + ": ";

        const userInput = await this.question(this.rl, promptMessage);
        if (!userInput.trim()) {
          resolve(["all"]);
          return;
        }
        const keysArray = userInput.split(/[,，\s]+/).filter(Boolean);
        resolve(keysArray);
      } catch (error) {
        console.error("获取键列表时出错:", error);
        reject(new Error("获取用户输入失败"));
      }
    });
  }

  langQusestion() {
    return new Promise(async (resolve, reject) => {
      try {
        const promptMessage =
          [
            "请输入需要修改的语言代码（多个语言用逗号分隔，例如：en,zh-CN）",
            `支持的语言: ${Object.keys(languageFileMap).join(", ")}`,
            "如需修改所有语言，请直接按回车",
          ].join("\n") + ": ";
        const userInput = (await this.question(this.rl, promptMessage)).trim();
        // 处理全选情况
        if (!userInput) {
          resolve(Object.values(languageFileMap));
          return;
        }
        // 处理语言列表输入
        const languages = userInput.split(/[,，\s]+/).filter(Boolean);
        const invalidLanguages = languages.filter(
          (lang) => !languageFileMap[lang]
        );
        if (invalidLanguages.length > 0) {
          console.error(
            `错误：以下语言代码不存在 - ${invalidLanguages.join(", ")}`
          );
          reject(new Error(`无效语言代码: ${invalidLanguages.join(", ")}`));
          return;
        }
        resolve(languages);
      } catch (error) {
        console.error("获取语言列表时出错:", error);
        reject(new Error("获取语言输入失败"));
      }
    });
  }
  exit() {
    console.log("程序退出");
    process.exit(1);
  }
}

// 启动工具
new I18n().start().catch((err) => {
  console.error("程序运行出错:", err);
  process.exit(1);
});
